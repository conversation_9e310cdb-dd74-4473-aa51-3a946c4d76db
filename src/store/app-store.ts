import { create } from 'zustand'
import { shallow } from 'zustand/shallow'
import { persist } from 'zustand/middleware'

interface SearchState {
  searchHistory: string[]
  recentFilters: Record<string, any>[]
  addSearchTerm: (term: string) => void
  addFilter: (filter: Record<string, any>) => void
  clearSearchHistory: () => void
}

interface UIState {
  sidebarOpen: boolean
  theme: 'light'
  viewMode: 'grid' | 'list'
  itemsPerPage: number
  setSidebarOpen: (open: boolean) => void

  setViewMode: (mode: 'grid' | 'list') => void
  setItemsPerPage: (count: number) => void
}

interface NotificationState {
  notifications: Array<{
    id: string
    title: string
    message: string
    type: 'info' | 'success' | 'warning' | 'error'
    read: boolean
    timestamp: Date
  }>
  unreadCount: number
  addNotification: (notification: Omit<NotificationState['notifications'][0], 'id' | 'timestamp'>) => void
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  removeNotification: (id: string) => void
  clearAll: () => void
}

type AppState = SearchState & UIState & NotificationState

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Search State
      searchHistory: [],
      recentFilters: [],
      addSearchTerm: (term: string) => {
        const { searchHistory } = get()
        const trimmedTerm = term.trim()
        if (!trimmedTerm || searchHistory.includes(trimmedTerm)) return
        
        set({
          searchHistory: [trimmedTerm, ...searchHistory.slice(0, 9)] // Keep last 10 searches
        })
      },
      addFilter: (filter: Record<string, any>) => {
        const { recentFilters } = get()
        set({
          recentFilters: [filter, ...recentFilters.slice(0, 4)] // Keep last 5 filters
        })
      },
      clearSearchHistory: () => set({ searchHistory: [], recentFilters: [] }),

      // UI State
      sidebarOpen: false,
      theme: 'light' as const,
      viewMode: 'grid',
      itemsPerPage: 20,
      setSidebarOpen: (open: boolean) => set({ sidebarOpen: open }),

      setViewMode: (mode: 'grid' | 'list') => set({ viewMode: mode }),
      setItemsPerPage: (count: number) => set({ itemsPerPage: count }),

      // Notification State
      notifications: [],
      unreadCount: 0,
      addNotification: (notification) => {
        const id = Math.random().toString(36).substr(2, 9)
        const newNotification = {
          ...notification,
          id,
          timestamp: new Date(),
          read: false
        }
        
        set((state) => ({
          notifications: [newNotification, ...state.notifications],
          unreadCount: state.unreadCount + 1
        }))
      },
      markAsRead: (id: string) => {
        set((state) => ({
          notifications: state.notifications.map(n => 
            n.id === id ? { ...n, read: true } : n
          ),
          unreadCount: Math.max(0, state.unreadCount - 1)
        }))
      },
      markAllAsRead: () => {
        set((state) => ({
          notifications: state.notifications.map(n => ({ ...n, read: true })),
          unreadCount: 0
        }))
      },
      removeNotification: (id: string) => {
        set((state) => {
          const notification = state.notifications.find(n => n.id === id)
          return {
            notifications: state.notifications.filter(n => n.id !== id),
            unreadCount: notification && !notification.read 
              ? Math.max(0, state.unreadCount - 1) 
              : state.unreadCount
          }
        })
      },
      clearAll: () => set({ notifications: [], unreadCount: 0 })
    }),
    {
      name: 'library-app-store',
      partialize: (state) => ({
        // Only persist certain parts of the state
        searchHistory: state.searchHistory,
        recentFilters: state.recentFilters,

        viewMode: state.viewMode,
        itemsPerPage: state.itemsPerPage
      })
    }
  )
)

// Selectors for better performance
export const useSearchHistory = () => useAppStore(state => state.searchHistory)
export const useRecentFilters = () => useAppStore(state => state.recentFilters)
export const useTheme = () => useAppStore(state => state.theme)
export const useViewMode = () => useAppStore(state => state.viewMode)
export const useNotifications = () => useAppStore(state => ({
  notifications: state.notifications,
  unreadCount: state.unreadCount,
  addNotification: state.addNotification,
  markAsRead: state.markAsRead,
  markAllAsRead: state.markAllAsRead,
  removeNotification: state.removeNotification,
  clearAll: state.clearAll
}), shallow)
export const useSidebar = () => useAppStore(state => ({
  isOpen: state.sidebarOpen,
  setOpen: state.setSidebarOpen
}), shallow)
