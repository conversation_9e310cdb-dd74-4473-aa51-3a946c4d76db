import { <PERSON>ada<PERSON> } from "next"
import { Suspense } from "react"
import { CategoryGrid } from "@/components/features/categories/category-grid"
import { Breadcrumbs } from "@/components/layout/breadcrumbs"
import { SectionLoading } from "@/components/ui/loading"
import { getCategories } from "@/lib/actions/categories"

export const metadata: Metadata = {
  title: "Categories | Library Management System",
  description: "Browse books by category in our digital library",
}

async function CategoriesSection() {
  try {
    const categories = await getCategories()
    return (
      <CategoryGrid
        categories={categories}
        title="All Categories"
        subtitle="Browse our complete collection of book categories"
      />
    )
  } catch (error) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">Unable to load categories at this time.</p>
      </div>
    )
  }
}

export default function CategoriesPage() {
  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Categories', current: true }
  ]

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumbs */}
      <Breadcrumbs items={breadcrumbItems} className="mb-6" />

      {/* Categories */}
      <Suspense fallback={<SectionLoading message="Loading categories..." />}>
        <CategoriesSection />
      </Suspense>
    </div>
  )
}
