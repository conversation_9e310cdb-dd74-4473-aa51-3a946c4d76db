import { <PERSON>ada<PERSON> } from "next"
import { notFound } from "next/navigation"
import { Suspense } from "react"
import { Breadcrumbs } from "@/components/layout/breadcrumbs"
import { SectionLoading } from "@/components/ui/loading"
import { getCategoryBySlug } from "@/lib/actions/categories"
import { getBooksByCategory } from "@/lib/actions/books"
import { CategoryBookGridWrapper } from "./category-book-grid-wrapper"

interface CategoryPageProps {
  params: {
    slug: string
  }
  searchParams: {
    [key: string]: string | string[] | undefined
  }
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  try {
    const { slug } = await params
    const category = await getCategoryBySlug(slug)
    
    if (!category) {
      return {
        title: "Category Not Found | Library Management System"
      }
    }

    return {
      title: `${category.name} Books | Library Management System`,
      description: category.description || `Browse ${category.name} books in our digital library`,
    }
  } catch (error) {
    return {
      title: "Category Not Found | Library Management System"
    }
  }
}

async function CategoryBooksSection({ slug, page }: { slug: string, page: number }) {
  try {
    const [category, books] = await Promise.all([
      getCategoryBySlug(slug),
      getBooksByCategory(slug, { page, limit: 20 })
    ])

    if (!category) {
      notFound()
    }

    return (
      <div className="space-y-8">
        {/* Category Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold tracking-tight">{category.name}</h1>
          {category.description && (
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              {category.description}
            </p>
          )}
        </div>

        {/* Books Grid */}
        <CategoryBookGridWrapper
          books={books.books}
          totalCount={books.totalCount}
          currentPage={books.currentPage}
          totalPages={books.totalPages}
        />
      </div>
    )
  } catch (error) {
    console.error('Error loading category books:', error)
    notFound()
  }
}

export default async function CategoryPage({ params, searchParams }: CategoryPageProps) {
  const { slug } = await params
  const { page: pageParam } = await searchParams
  const page = Number(pageParam || '1')
  
  const category = await getCategoryBySlug(slug)

  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Categories', href: '/categories' },
    { label: category ? category.name : 'Category', current: true }
  ]

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumbs */}
      <Breadcrumbs items={breadcrumbItems} className="mb-6" />

      {/* Category Books */}
      <Suspense fallback={<SectionLoading message="Loading category books..." />}>
        <CategoryBooksSection slug={slug} page={page} />
      </Suspense>
    </div>
  )
}
