"use client"

import { BookGrid } from "@/components/features/books/book-grid"
import type { BookWithDetails } from "@/types"
import { usePathname, useRouter, useSearchParams } from "next/navigation"

interface CategoryBookGridWrapperProps {
  books: BookWithDetails[]
  totalCount: number
  currentPage: number
  totalPages: number
}

export function CategoryBookGridWrapper({
  books,
  totalCount,
  currentPage,
  totalPages,
}: CategoryBookGridWrapperProps) {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set('page', page.toString())
    router.push(`${pathname}?${params.toString()}`)
  }

  return (
    <BookGrid
      books={books}
      totalCount={totalCount}
      currentPage={currentPage}
      totalPages={totalPages}
      onPageChange={handlePageChange}
      viewMode="grid"
    />
  )
}
