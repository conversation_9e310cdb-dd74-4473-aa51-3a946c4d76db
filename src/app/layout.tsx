import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Auth<PERSON><PERSON><PERSON>, QueryProvider, ToastProvider, ThemeProvider, NotificationProvider } from "@/components/providers";
import { <PERSON>er, Footer } from "@/components/layout";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Library Management System",
  description: "A comprehensive digital library management system for modern libraries",
  keywords: ["library", "books", "management", "digital library", "catalog"],
  authors: [{ name: "Library Management Team" }],
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <ThemeProvider defaultTheme="system">
          <QueryProvider>
            <AuthProvider>
              <ToastProvider>
                <NotificationProvider>
                  <div className="relative flex min-h-screen flex-col">
                    <Header />
                    <main className="flex-1">
                      {children}
                    </main>
                    <Footer />
                  </div>
                </NotificationProvider>
              </ToastProvider>
            </AuthProvider>
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
