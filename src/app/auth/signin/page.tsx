import { Metadata } from "next"
import { Suspense } from "react"
import { LoginForm } from "@/components/forms/login-form"
import { PageLoading } from "@/components/ui/loading"

export const metadata: Metadata = {
  title: "Sign In | Library Management System",
  description: "Sign in to your library account",
}

export default function SignInPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-muted/50 px-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="mx-auto h-12 w-12 rounded-lg bg-primary flex items-center justify-center mb-4">
            <svg
              className="h-7 w-7 text-primary-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
              />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-foreground">Library Management</h1>
          <p className="text-muted-foreground mt-2">
            Welcome back to your digital library
          </p>
        </div>

        <Suspense fallback={<PageLoading />}>
          <LoginForm />
        </Suspense>
      </div>
    </div>
  )
}
