import { Suspense } from "react"
import { HeroSection } from "@/components/features/home/<USER>"
import { FeaturedBooks } from "@/components/features/books/featured-books"
import { CategoryGrid } from "@/components/features/categories/category-grid"
import { TotalBooksCard, ActiveUsersCard, BorrowedBooksCard } from "@/components/features/dashboard/stats-card"
import { SectionLoading } from "@/components/ui/loading"
import { getFeaturedBooks } from "@/lib/actions/books"
import { getCategories } from "@/lib/actions/categories"
import { getAdminDashboardStats } from "@/lib/actions/dashboard"

async function FeaturedBooksSection() {
  try {
    const featuredBooks = await getFeaturedBooks()
    return (
      <FeaturedBooks
        books={featuredBooks}
        title="Featured Books"
        subtitle="Discover our most popular and recently added titles"
        autoPlay={true}
      />
    )
  } catch (error) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">Unable to load featured books at this time.</p>
      </div>
    )
  }
}

async function CategoriesSection() {
  try {
    const categories = await getCategories()
    return (
      <CategoryGrid
        categories={categories}
        title="Explore Categories"
        subtitle="Browse books by your favorite topics and genres"
        maxItems={6}
      />
    )
  } catch (error) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">Unable to load categories at this time.</p>
      </div>
    )
  }
}

async function StatsSection() {
  try {
    const stats = await getAdminDashboardStats()
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <TotalBooksCard count={stats.totalBooks} />
        <ActiveUsersCard count={stats.totalUsers} />
        <BorrowedBooksCard count={stats.totalBorrows} />
      </div>
    )
  } catch (error) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <TotalBooksCard count={0} />
        <ActiveUsersCard count={0} />
        <BorrowedBooksCard count={0} />
      </div>
    )
  }
}

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <HeroSection />

      {/* Main Content */}
      <div className="container mx-auto px-4 py-16 space-y-16">
        {/* Library Statistics */}
        <section className="space-y-6">
          <div className="text-center space-y-2">
            <h2 className="text-3xl font-bold tracking-tight">Library at a Glance</h2>
            <p className="text-muted-foreground">
              See what's happening in our digital library community
            </p>
          </div>
          <Suspense fallback={<SectionLoading message="Loading statistics..." />}>
            <StatsSection />
          </Suspense>
        </section>

        {/* Featured Books */}
        <Suspense fallback={<SectionLoading message="Loading featured books..." />}>
          <FeaturedBooksSection />
        </Suspense>

        {/* Categories */}
        <Suspense fallback={<SectionLoading message="Loading categories..." />}>
          <CategoriesSection />
        </Suspense>

        {/* Call to Action */}
        <section className="text-center py-16 bg-muted/50 rounded-lg">
          <div className="space-y-4">
            <h2 className="text-3xl font-bold tracking-tight">Ready to Start Reading?</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Join thousands of readers who have discovered their next favorite book through our digital library.
              Sign up today and start your reading journey.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-4">
              <a
                href="/auth/signup"
                className="inline-flex items-center justify-center rounded-md bg-primary px-8 py-3 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
              >
                Create Free Account
              </a>
              <a
                href="/books"
                className="inline-flex items-center justify-center rounded-md border border-input bg-background px-8 py-3 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
              >
                Browse Books
              </a>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}
