import { Metadata } from "next"
import { notFound } from "next/navigation"
import { Suspense } from "react"
import { BookDetails } from "@/components/features/books/book-details"
import { Breadcrumbs } from "@/components/layout/breadcrumbs"
import { PageLoading } from "@/components/ui/loading"
import { getBookById } from "@/lib/actions/books"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"

interface BookPageProps {
  params: Promise<{
    id: string
  }>
}

export async function generateMetadata({ params }: BookPageProps): Promise<Metadata> {
  try {
    const { id } = await params
    const book = await getBookById(id)
    
    if (!book) {
      return {
        title: "Book Not Found | Library Management System"
      }
    }

    return {
      title: `${book.title} by ${book.author} | Library Management System`,
      description: book.description || `${book.title} by ${book.author} - Available in our digital library`,
      keywords: [book.title, book.author, ...(book.categories?.map(c => c.name) || [])],
    }
  } catch (error) {
    return {
      title: "Book Not Found | Library Management System"
    }
  }
}

async function BookDetailsSection({ bookId }: { bookId: string }) {
  try {
    const [book, session] = await Promise.all([
      getBookById(bookId),
      getServerSession(authOptions)
    ])

    const user = session?.user as any

    if (!book) {
      notFound()
    }

    // Get user's active borrows count if user is logged in
    let userActiveBorrows = 0
    if (user) {
      // This would need to be implemented in the user actions
      // userActiveBorrows = await getUserActiveBorrowsCount(user.id)
    }

    return (
      <BookDetails
        book={book}
        userRole={user?.role}
        userActiveBorrows={userActiveBorrows}
        maxBorrows={5}
      />
    )
  } catch (error) {
    console.error('Error loading book details:', error)
    notFound()
  }
}

export default async function BookPage({ params }: BookPageProps) {
  const { id } = await params
  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Books', href: '/books' },
    { label: 'Book Details', current: true }
  ]

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumbs */}
      <Breadcrumbs items={breadcrumbItems} className="mb-6" />

      {/* Book Details */}
      <Suspense fallback={<PageLoading />}>
        <BookDetailsSection bookId={id} />
      </Suspense>
    </div>
  )
}
