'use client'

import * as React from "react"
import { useSearchParams } from "next/navigation"
import { BookFilters } from "@/components/features/books/book-filters"
import { BookGrid } from "@/components/features/books/book-grid"
import { Breadcrumbs } from "@/components/layout/breadcrumbs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useToast } from "@/components/ui/toast"
import { searchBooks } from "@/lib/actions/books"
import { getCategories } from "@/lib/actions/categories"
import type { BookWithDetails, Category } from "@/types"

interface SearchResult {
  books: BookWithDetails[]
  totalCount: number
  totalPages: number
  currentPage: number
}

export default function BookSearchPage() {
  const searchParams = useSearchParams()
  const { addToast } = useToast()
  
  const [books, setBooks] = React.useState<SearchResult>({
    books: [],
    totalCount: 0,
    totalPages: 0,
    currentPage: 1
  })
  const [categories, setCategories] = React.useState<Category[]>([])
  const [loading, setLoading] = React.useState(true)
  const [viewMode, setViewMode] = React.useState<'grid' | 'list'>('grid')

  // Load categories on mount
  React.useEffect(() => {
    const loadCategories = async () => {
      try {
        const categoriesData = await getCategories()
        setCategories(categoriesData)
      } catch (error) {
        console.error('Failed to load categories:', error)
      }
    }
    loadCategories()
  }, [])

  // Load books when search params change
  React.useEffect(() => {
    const loadBooks = async () => {
      setLoading(true)
      try {
        const params = {
          query: searchParams.get('q') || '',
          categories: searchParams.get('categories')?.split(',').filter(Boolean) || [],
          availability: searchParams.get('availability') || 'all',
          sortBy: searchParams.get('sortBy') || 'relevance',
          language: searchParams.get('language') || 'all',
          yearFrom: searchParams.get('yearFrom') ? parseInt(searchParams.get('yearFrom')!) : undefined,
          yearTo: searchParams.get('yearTo') ? parseInt(searchParams.get('yearTo')!) : undefined,
          page: parseInt(searchParams.get('page') || '1'),
          limit: 20
        }

        const result = await searchBooks(params)
        setBooks(result)
      } catch (error) {
        console.error('Failed to load books:', error)
        addToast({
          title: "Error",
          description: "Failed to load books. Please try again.",
          variant: "destructive"
        })
      } finally {
        setLoading(false)
      }
    }

    loadBooks()
  }, [searchParams, addToast])

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set('page', page.toString())
    const newUrl = `/books/search?${params.toString()}`
    window.location.href = newUrl
  }

  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Books', href: '/books' },
    { label: 'Advanced Search', current: true }
  ]

  const searchQuery = searchParams.get('q') || ''

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumbs */}
      <Breadcrumbs items={breadcrumbItems} className="mb-6" />

      {/* Page Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Advanced Search</h1>
            {searchQuery && (
              <p className="text-muted-foreground mt-2">
                Search results for "{searchQuery}"
              </p>
            )}
          </div>
          
          <div className="hidden sm:flex space-x-2">
            <Button variant="outline" onClick={() => window.location.href = '/books'}>
              Browse All Books
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Filters Sidebar */}
        <div className="lg:col-span-1">
          <div className="sticky top-24">
            <BookFilters categories={categories} />
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          <BookGrid
            books={books.books}
            totalCount={books.totalCount}
            currentPage={books.currentPage}
            totalPages={books.totalPages}
            onPageChange={handlePageChange}
            loading={loading}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
          />
        </div>
      </div>
    </div>
  )
}
