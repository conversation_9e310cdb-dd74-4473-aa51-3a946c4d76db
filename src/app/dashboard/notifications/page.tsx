import { Suspense } from "react"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { NotificationList } from "@/components/features/notifications/notification-list"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { SectionLoading } from "@/components/ui/loading"
import { getUserNotifications, markAllNotificationsAsRead } from "@/lib/actions/notifications"

async function NotificationsSection({ userId }: { userId: string }) {
  try {
    const notifications = await getUserNotifications(userId)
    
    if (!notifications.length) {
      return (
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-muted-foreground"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h8a2 2 0 012 2v4"
            />
          </svg>
          <h3 className="mt-4 text-lg font-semibold">No notifications</h3>
          <p className="text-muted-foreground">
            You're all caught up! No new notifications.
          </p>
        </div>
      )
    }

    return <NotificationList notifications={notifications} />
  } catch (error) {
    console.error('Failed to load notifications:', error)
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Unable to load notifications at this time.</p>
      </div>
    )
  }
}

async function markAllAsRead(userId: string) {
  'use server'
  try {
    await markAllNotificationsAsRead(userId)
  } catch (error) {
    console.error('Failed to mark notifications as read:', error)
  }
}

export default async function NotificationsPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect('/auth/signin?callbackUrl=/dashboard/notifications')
  }

  const user = session.user as any

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Notifications</h1>
          <p className="text-muted-foreground">
            Stay updated with your library activities
          </p>
        </div>
        <form action={markAllAsRead.bind(null, user.id)}>
          <Button type="submit" variant="outline">
            Mark All as Read
          </Button>
        </form>
      </div>

      {/* Notifications */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Notifications</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<SectionLoading message="Loading notifications..." />}>
            <NotificationsSection userId={user.id} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}
