import { Suspense } from "react"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { RecentActivity } from "@/components/features/dashboard/recent-activity"
import { QuickActions } from "@/components/features/dashboard/quick-actions"
import { StatsCard } from "@/components/features/dashboard/stats-card"
import { SectionLoading } from "@/components/ui/loading"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { getUserActiveBorrows } from "@/lib/actions/borrows"
import { getUserStats } from "@/lib/actions/users"
import { ActiveBorrowsClient } from "./active-borrows-client"

async function WelcomeSection({ user }: { user: any }) {
  const currentHour = new Date().getHours()
  const greeting = currentHour < 12 ? 'Good morning' : currentHour < 18 ? 'Good afternoon' : 'Good evening'

  return (
    <div className="space-y-2">
      <h1 className="text-3xl font-bold tracking-tight">
        {greeting}, {user.name}!
      </h1>
      <p className="text-muted-foreground">
        Welcome back to your library dashboard. Here's what's happening with your account.
      </p>
    </div>
  )
}

async function UserStatsSection({ userId }: { userId: string }) {
  try {
    const stats = await getUserStats(userId)
    
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatsCard
          title="Books Borrowed"
          value={stats.totalBorrows}
          icon={
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          }
          color="primary"
        />
        <StatsCard
          title="Currently Borrowed"
          value={stats.activeBorrows}
          icon={
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          }
          color="success"
        />
        <StatsCard
          title="Overdue Books"
          value={stats.overdueBooks}
          icon={
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          }
          color={stats.overdueBooks > 0 ? "danger" : "success"}
        />
      </div>
    )
  } catch (error) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatsCard title="Books Borrowed" value={0} loading />
        <StatsCard title="Currently Borrowed" value={0} loading />
        <StatsCard title="Overdue Books" value={0} loading />
      </div>
    )
  }
}

async function ActiveBorrowsSection({ userId }: { userId: string }) {
  try {
    const activeBorrows = await getUserActiveBorrows(userId)
    return <ActiveBorrowsClient activeBorrows={activeBorrows} />
  } catch (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Active Borrows</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">Unable to load active borrows.</p>
          </div>
        </CardContent>
      </Card>
    )
  }
}

async function RecentActivitySection({ userId }: { userId: string }) {
  // This would need to be implemented in the actions
  const mockActivities = [
    {
      id: '1',
      type: 'BORROW_APPROVED' as const,
      title: 'Borrow request approved',
      description: 'Your request for "The Great Gatsby" has been approved',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      bookId: '1',
      bookTitle: 'The Great Gatsby'
    },
    {
      id: '2',
      type: 'BOOK_RETURNED' as const,
      title: 'Book returned',
      description: 'You returned "To Kill a Mockingbird"',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
      bookId: '2',
      bookTitle: 'To Kill a Mockingbird'
    }
  ]

  return <RecentActivity activities={mockActivities} maxItems={5} />
}

export default async function DashboardPage() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user) {
    redirect('/auth/signin?callbackUrl=/dashboard')
  }

  const user = session.user as any

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <WelcomeSection user={user} />

      {/* User Statistics */}
      <Suspense fallback={<SectionLoading message="Loading your statistics..." />}>
        <UserStatsSection userId={user.id} />
      </Suspense>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Active Borrows */}
        <Suspense fallback={<SectionLoading message="Loading active borrows..." />}>
          <ActiveBorrowsSection userId={user.id} />
        </Suspense>

        {/* Recent Activity */}
        <Suspense fallback={<SectionLoading message="Loading recent activity..." />}>
          <RecentActivitySection userId={user.id} />
        </Suspense>
      </div>

      {/* Quick Actions */}
      <QuickActions userRole={user.role} />
    </div>
  )
}
