import { Suspense } from "react"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { BorrowCard } from "@/components/features/borrows/borrow-card"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardT<PERSON>le } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { SectionLoading } from "@/components/ui/loading"
import { getUserActiveBorrows } from "@/lib/actions/borrows"
import { getUserBorrowRequests } from "@/lib/actions/borrows"

async function ActiveBorrowsSection({ userId }: { userId: string }) {
  try {
    const activeBorrows = await getUserActiveBorrows(userId)
    
    if (!activeBorrows.length) {
      return (
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-muted-foreground"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          <h3 className="mt-4 text-lg font-semibold">No active borrows</h3>
          <p className="text-muted-foreground mb-6">
            You don't have any books currently borrowed.
          </p>
          <Button onClick={() => window.location.href = "/books"}>
            Browse Books
          </Button>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        {activeBorrows.map((borrow) => (
          <BorrowCard key={borrow.id} borrow={borrow} />
        ))}
      </div>
    )
  } catch (error) {
    console.error('Failed to load active borrows:', error)
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Unable to load active borrows.</p>
      </div>
    )
  }
}

async function BorrowRequestsSection({ userId }: { userId: string }) {
  try {
    const requests = await getUserBorrowRequests(userId)
    
    if (!requests.length) {
      return (
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-muted-foreground"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
            />
          </svg>
          <h3 className="mt-4 text-lg font-semibold">No pending requests</h3>
          <p className="text-muted-foreground mb-6">
            You don't have any pending borrow requests.
          </p>
          <Button onClick={() => window.location.href = "/books"}>
            Browse Books
          </Button>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        {requests.map((request) => (
          <Card key={request.id}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h4 className="font-semibold">{request.bookCopy.book.title}</h4>
                  <p className="text-sm text-muted-foreground">
                    by {request.bookCopy.book.author}
                  </p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Requested: {new Date(request.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div className="text-right">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    request.status === 'PENDING' 
                      ? 'bg-yellow-100 text-yellow-800'
                      : request.status === 'APPROVED'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {request.status}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  } catch (error) {
    console.error('Failed to load borrow requests:', error)
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Unable to load borrow requests.</p>
      </div>
    )
  }
}

export default async function MyBooksPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect('/auth/signin?callbackUrl=/dashboard/my-books')
  }

  const user = session.user as any

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Books</h1>
          <p className="text-muted-foreground">
            Manage your borrowed books and requests
          </p>
        </div>
        <Button onClick={() => window.location.href = "/books"}>
          Browse Books
        </Button>
      </div>

      {/* Books Tabs */}
      <Tabs defaultValue="active" className="space-y-6">
        <TabsList>
          <TabsTrigger value="active">Active Borrows</TabsTrigger>
          <TabsTrigger value="requests">Pending Requests</TabsTrigger>
          <TabsTrigger value="history">Borrow History</TabsTrigger>
        </TabsList>

        <TabsContent value="active">
          <Card>
            <CardHeader>
              <CardTitle>Currently Borrowed Books</CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<SectionLoading message="Loading active borrows..." />}>
                <ActiveBorrowsSection userId={user.id} />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="requests">
          <Card>
            <CardHeader>
              <CardTitle>Pending Requests</CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<SectionLoading message="Loading requests..." />}>
                <BorrowRequestsSection userId={user.id} />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Borrow History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <p className="text-muted-foreground">Borrow history feature coming soon.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
