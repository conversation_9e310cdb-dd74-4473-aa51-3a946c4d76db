import { Suspense } from "react"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { ProfileForm } from "@/components/forms/profile-form"
import { PasswordChangeForm } from "@/components/forms/password-change-form"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { SectionLoading } from "@/components/ui/loading"
import { getUserById } from "@/lib/actions/users"

async function ProfileSection({ userId }: { userId: string }) {
  try {
    const user = await getUserById(userId)
    
    if (!user) {
      return (
        <div className="text-center py-8">
          <p className="text-muted-foreground">User not found.</p>
        </div>
      )
    }

    return <ProfileForm user={user} />
  } catch (error) {
    console.error('Failed to load user profile:', error)
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Unable to load profile at this time.</p>
      </div>
    )
  }
}

async function PasswordSection({ userId }: { userId: string }) {
  return <PasswordChangeForm userId={userId} />
}

export default async function ProfilePage() {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect('/auth/signin?callbackUrl=/dashboard/profile')
  }

  const user = session.user as any

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Profile Settings</h1>
        <p className="text-muted-foreground">
          Manage your account settings and preferences
        </p>
      </div>

      {/* Profile Tabs */}
      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList>
          <TabsTrigger value="profile">Profile Information</TabsTrigger>
          <TabsTrigger value="password">Change Password</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<SectionLoading message="Loading profile..." />}>
                <ProfileSection userId={user.id} />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="password">
          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<SectionLoading message="Loading..." />}>
                <PasswordSection userId={user.id} />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preferences">
          <Card>
            <CardHeader>
              <CardTitle>Preferences</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium">Email Notifications</h4>
                    <p className="text-sm text-muted-foreground">
                      Receive email notifications for due dates and updates
                    </p>
                  </div>
                  <input type="checkbox" className="rounded" defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium">SMS Notifications</h4>
                    <p className="text-sm text-muted-foreground">
                      Receive SMS notifications for overdue books
                    </p>
                  </div>
                  <input type="checkbox" className="rounded" />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium">Book Recommendations</h4>
                    <p className="text-sm text-muted-foreground">
                      Show personalized book recommendations
                    </p>
                  </div>
                  <input type="checkbox" className="rounded" defaultChecked />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
