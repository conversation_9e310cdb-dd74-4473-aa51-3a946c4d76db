"use client"

import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/features/borrows/borrow-card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import type { BorrowWithDetails } from "@/types"

interface ActiveBorrowsClientProps {
  activeBorrows: BorrowWithDetails[]
}

export function ActiveBorrowsClient({ activeBorrows }: ActiveBorrowsClientProps) {
  if (!activeBorrows.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Active Borrows</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <svg
              className="mx-auto h-12 w-12 text-muted-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <h3 className="mt-4 text-lg font-semibold">No active borrows</h3>
            <p className="text-muted-foreground">
              You don't have any books currently borrowed.
            </p>
            <div className="mt-6">
              <Button onClick={() => window.location.href = "/books"}>
                Browse Books
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Active Borrows ({activeBorrows.length})</CardTitle>
        <Button variant="outline" size="sm" onClick={() => window.location.href = "/dashboard/my-books"}>
          View All
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activeBorrows.slice(0, 3).map((borrow) => (
            <BorrowCard key={borrow.id} borrow={borrow} variant="compact" />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
