import { Suspense } from "react"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { CategoryGrid } from "@/components/features/categories/category-grid"
import { CategoryForm } from "@/components/forms/category-form"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { SectionLoading } from "@/components/ui/loading"
import { getCategories } from "@/lib/actions/categories"

async function CategoriesSection() {
  try {
    const categories = await getCategories()
    
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            {categories.length} categor{categories.length !== 1 ? 'ies' : 'y'} found
          </p>
          <Dialog>
            <DialogTrigger asChild>
              <Button>Add New Category</Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Category</DialogTitle>
              </DialogHeader>
              <CategoryForm 
                mode="create"
                onSuccess={() => window.location.reload()}
                onCancel={() => {}}
              />
            </DialogContent>
          </Dialog>
        </div>

        <CategoryGrid categories={categories} showActions={true} />
      </div>
    )
  } catch (error) {
    console.error('Failed to load categories:', error)
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Unable to load categories at this time.</p>
      </div>
    )
  }
}

export default async function AdminCategoriesPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect('/auth/signin?callbackUrl=/dashboard/admin/categories')
  }

  const user = session.user as any
  
  // Check if user has admin/librarian role
  if (!['ADMIN', 'LIBRARIAN'].includes(user.role)) {
    redirect('/dashboard')
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Category Management</h1>
          <p className="text-muted-foreground">
            Organize your library's book categories
          </p>
        </div>
        <Button onClick={() => window.location.href = "/dashboard/admin/books"}>
          Back to Books
        </Button>
      </div>

      {/* Categories Management */}
      <Card>
        <CardHeader>
          <CardTitle>Book Categories</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<SectionLoading message="Loading categories..." />}>
            <CategoriesSection />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}
