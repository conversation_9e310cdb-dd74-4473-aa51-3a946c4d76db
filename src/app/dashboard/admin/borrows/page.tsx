import { Suspense } from "react"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { BorrowList } from "@/components/features/borrows/borrow-list"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { SectionLoading } from "@/components/ui/loading"
import { getAllActiveBorrows, getPendingBorrowRequests, getOverdueBooks } from "@/lib/actions/borrows"

async function ActiveBorrowsSection() {
  try {
    const activeBorrows = await getAllActiveBorrows()
    
    if (!activeBorrows.length) {
      return (
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-muted-foreground"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          <h3 className="mt-4 text-lg font-semibold">No active borrows</h3>
          <p className="text-muted-foreground">
            There are currently no active book borrows.
          </p>
        </div>
      )
    }

    return <BorrowList borrows={activeBorrows} showActions={true} />
  } catch (error) {
    console.error('Failed to load active borrows:', error)
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Unable to load active borrows.</p>
      </div>
    )
  }
}

async function PendingRequestsSection() {
  try {
    const pendingRequests = await getPendingBorrowRequests()
    
    if (!pendingRequests.length) {
      return (
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-muted-foreground"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
            />
          </svg>
          <h3 className="mt-4 text-lg font-semibold">No pending requests</h3>
          <p className="text-muted-foreground">
            There are no pending borrow requests to review.
          </p>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        {pendingRequests.map((request) => (
          <Card key={request.id}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h4 className="font-semibold">{request.bookCopy.book.title}</h4>
                  <p className="text-sm text-muted-foreground">
                    by {request.bookCopy.book.author}
                  </p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Requested by: {request.user.name} ({request.user.email})
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Requested: {new Date(request.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <Button size="sm" variant="outline">
                    Reject
                  </Button>
                  <Button size="sm">
                    Approve
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  } catch (error) {
    console.error('Failed to load pending requests:', error)
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Unable to load pending requests.</p>
      </div>
    )
  }
}

async function OverdueBooksSection() {
  try {
    const overdueBooks = await getOverdueBooks()
    
    if (!overdueBooks.length) {
      return (
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-green-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M5 13l4 4L19 7"
            />
          </svg>
          <h3 className="mt-4 text-lg font-semibold">No overdue books</h3>
          <p className="text-muted-foreground">
            All books are returned on time. Great job!
          </p>
        </div>
      )
    }

    return <BorrowList borrows={overdueBooks} showActions={true} variant="overdue" />
  } catch (error) {
    console.error('Failed to load overdue books:', error)
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Unable to load overdue books.</p>
      </div>
    )
  }
}

export default async function AdminBorrowsPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect('/auth/signin?callbackUrl=/dashboard/admin/borrows')
  }

  const user = session.user as any
  
  // Check if user has admin/librarian role
  if (!['ADMIN', 'LIBRARIAN'].includes(user.role)) {
    redirect('/dashboard')
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Borrow Management</h1>
          <p className="text-muted-foreground">
            Manage book borrowing requests and active loans
          </p>
        </div>
      </div>

      {/* Borrow Management Tabs */}
      <Tabs defaultValue="pending" className="space-y-6">
        <TabsList>
          <TabsTrigger value="pending">Pending Requests</TabsTrigger>
          <TabsTrigger value="active">Active Borrows</TabsTrigger>
          <TabsTrigger value="overdue">Overdue Books</TabsTrigger>
        </TabsList>

        <TabsContent value="pending">
          <Card>
            <CardHeader>
              <CardTitle>Pending Borrow Requests</CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<SectionLoading message="Loading pending requests..." />}>
                <PendingRequestsSection />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="active">
          <Card>
            <CardHeader>
              <CardTitle>Active Borrows</CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<SectionLoading message="Loading active borrows..." />}>
                <ActiveBorrowsSection />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="overdue">
          <Card>
            <CardHeader>
              <CardTitle>Overdue Books</CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<SectionLoading message="Loading overdue books..." />}>
                <OverdueBooksSection />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
