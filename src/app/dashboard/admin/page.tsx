import { Suspense } from "react"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { OverviewStats } from "@/components/features/admin/overview-stats"
import { PendingRequests } from "@/components/features/admin/pending-requests"
import { OverdueManagement } from "@/components/features/admin/overdue-management"
import { RecentActivity } from "@/components/features/dashboard/recent-activity"
import { SectionLoading } from "@/components/ui/loading"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { getAdminDashboardStats } from "@/lib/actions/dashboard"
import { getPendingBorrowRequests, getOverdueBooks } from "@/lib/actions/borrows"

async function AdminStatsSection() {
  try {
    const stats = await getAdminDashboardStats()
    
    return (
      <OverviewStats
        stats={stats}
        trends={{
          booksGrowth: 12,
          usersGrowth: 8,
          borrowsGrowth: 15
        }}
      />
    )
  } catch (error) {
    console.error('Failed to load admin stats:', error)
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Unable to load statistics at this time.</p>
      </div>
    )
  }
}

async function PendingRequestsSection() {
  try {
    const pendingRequests = await getPendingBorrowRequests()
    
    const handleApprove = async (requestId: string) => {
      'use server'
      // This would be implemented in the borrow actions
      console.log('Approving request:', requestId)
    }

    const handleReject = async (requestId: string) => {
      'use server'
      // This would be implemented in the borrow actions
      console.log('Rejecting request:', requestId)
    }

    return (
      <PendingRequests
        requests={pendingRequests}
        onApprove={handleApprove}
        onReject={handleReject}
        maxItems={5}
      />
    )
  } catch (error) {
    console.error('Failed to load pending requests:', error)
    return (
      <Card>
        <CardHeader>
          <CardTitle>Pending Requests</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">Unable to load pending requests.</p>
          </div>
        </CardContent>
      </Card>
    )
  }
}

async function OverdueBooksSection() {
  try {
    const overdueBooks = await getOverdueBooks()
    
    const handleSendReminder = async (borrowId: string) => {
      'use server'
      // This would be implemented in the borrow actions
      console.log('Sending reminder for borrow:', borrowId)
    }

    const handleMarkReturned = async (borrowId: string) => {
      'use server'
      // This would be implemented in the borrow actions
      console.log('Marking as returned:', borrowId)
    }

    return (
      <OverdueManagement
        overdueBooks={overdueBooks}
        onSendReminder={handleSendReminder}
        onMarkReturned={handleMarkReturned}
        maxItems={5}
      />
    )
  } catch (error) {
    console.error('Failed to load overdue books:', error)
    return (
      <Card>
        <CardHeader>
          <CardTitle>Overdue Books</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">Unable to load overdue books.</p>
          </div>
        </CardContent>
      </Card>
    )
  }
}

async function AdminActivitySection() {
  // Mock data for admin activity
  const adminActivities = [
    {
      id: '1',
      type: 'BORROW_APPROVED' as const,
      title: 'Borrow request approved',
      description: 'Approved "The Great Gatsby" for John Doe',
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    },
    {
      id: '2',
      type: 'BOOK_RETURNED' as const,
      title: 'Book returned',
      description: 'Jane Smith returned "To Kill a Mockingbird"',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    },
    {
      id: '3',
      type: 'NOTIFICATION' as const,
      title: 'New user registered',
      description: 'Alice Johnson created a new account',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
    }
  ]

  return <RecentActivity activities={adminActivities} maxItems={5} />
}

export default async function AdminDashboardPage() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user) {
    redirect('/auth/signin?callbackUrl=/dashboard/admin')
  }

  const user = session.user as any
  
  // Check if user has admin or librarian role
  if (user.role !== 'ADMIN' && user.role !== 'LIBRARIAN') {
    redirect('/dashboard')
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Manage your library system and monitor key metrics
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => window.location.href = "/dashboard/admin/reports"}>
            View Reports
          </Button>
          <Button onClick={() => window.location.href = "/dashboard/admin/books/new"}>
            Add New Book
          </Button>
        </div>
      </div>

      {/* Overview Statistics */}
      <Suspense fallback={<SectionLoading message="Loading statistics..." />}>
        <AdminStatsSection />
      </Suspense>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Pending Requests */}
        <Suspense fallback={<SectionLoading message="Loading pending requests..." />}>
          <PendingRequestsSection />
        </Suspense>

        {/* Recent Activity */}
        <Suspense fallback={<SectionLoading message="Loading recent activity..." />}>
          <AdminActivitySection />
        </Suspense>
      </div>

      {/* Overdue Management */}
      <Suspense fallback={<SectionLoading message="Loading overdue books..." />}>
        <OverdueBooksSection />
      </Suspense>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2" onClick={() => window.location.href = "/dashboard/admin/books"}>
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
              <span className="text-sm font-medium">Manage Books</span>
            </Button>

            <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2" onClick={() => window.location.href = "/dashboard/admin/users"}>
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              <span className="text-sm font-medium">Manage Users</span>
            </Button>

            <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2" onClick={() => window.location.href = "/dashboard/admin/categories"}>
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14-7H5m14 14H5" />
              </svg>
              <span className="text-sm font-medium">Manage Categories</span>
            </Button>

            <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2" onClick={() => window.location.href = "/dashboard/admin/reports"}>
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <span className="text-sm font-medium">View Reports</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
