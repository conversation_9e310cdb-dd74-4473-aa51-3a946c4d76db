import { Suspense } from "react"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { UserList } from "@/components/features/users/user-list"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { SectionLoading } from "@/components/ui/loading"
import { getUsers } from "@/lib/actions/users"

interface SearchParams {
  search?: string
  role?: string
  page?: string
}

async function UsersSection({ searchParams }: { searchParams: SearchParams }) {
  try {
    const users = await getUsers({
      search: searchParams.search,
      role: searchParams.role,
      page: parseInt(searchParams.page || '1'),
      limit: 20
    })

    return (
      <div className="space-y-6">
        {/* Search and Filters */}
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <Input
              placeholder="Search users by name or email..."
              defaultValue={searchParams.search || ''}
              onChange={(e) => {
                const url = new URL(window.location.href)
                if (e.target.value) {
                  url.searchParams.set('search', e.target.value)
                } else {
                  url.searchParams.delete('search')
                }
                url.searchParams.delete('page')
                window.location.href = url.toString()
              }}
            />
          </div>
          <select
            className="px-3 py-2 border rounded-md"
            defaultValue={searchParams.role || ''}
            onChange={(e) => {
              const url = new URL(window.location.href)
              if (e.target.value) {
                url.searchParams.set('role', e.target.value)
              } else {
                url.searchParams.delete('role')
              }
              url.searchParams.delete('page')
              window.location.href = url.toString()
            }}
          >
            <option value="">All Roles</option>
            <option value="USER">Users</option>
            <option value="LIBRARIAN">Librarians</option>
            <option value="ADMIN">Admins</option>
          </select>
        </div>

        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            {users.total} user{users.total !== 1 ? 's' : ''} found
          </p>
        </div>

        <UserList users={users.users} />
        
        {users.totalPages > 1 && (
          <div className="flex justify-center">
            <div className="flex space-x-2">
              {Array.from({ length: users.totalPages }, (_, i) => i + 1).map((page) => (
                <Button
                  key={page}
                  variant={page === users.page ? "default" : "outline"}
                  size="sm"
                  onClick={() => {
                    const url = new URL(window.location.href)
                    url.searchParams.set('page', page.toString())
                    window.location.href = url.toString()
                  }}
                >
                  {page}
                </Button>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  } catch (error) {
    console.error('Failed to load users:', error)
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Unable to load users at this time.</p>
      </div>
    )
  }
}

export default async function AdminUsersPage({
  searchParams
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect('/auth/signin?callbackUrl=/dashboard/admin/users')
  }

  const user = session.user as any
  
  // Check if user has admin role
  if (user.role !== 'ADMIN') {
    redirect('/dashboard')
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
          <p className="text-muted-foreground">
            Manage user accounts and permissions
          </p>
        </div>
      </div>

      {/* Users Management */}
      <Card>
        <CardHeader>
          <CardTitle>System Users</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<SectionLoading message="Loading users..." />}>
            <UsersSection searchParams={searchParams} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}
