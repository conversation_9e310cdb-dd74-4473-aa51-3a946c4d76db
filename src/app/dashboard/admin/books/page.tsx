import { Suspense } from "react"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { BookGrid } from "@/components/features/books/book-grid"
import { BookFilters } from "@/components/features/books/book-filters"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { SectionLoading } from "@/components/ui/loading"
import { searchBooks } from "@/lib/actions/books"
import { getCategories } from "@/lib/actions/categories"

interface SearchParams {
  query?: string
  category?: string
  page?: string
  sortBy?: string
}

async function BooksSection({ searchParams }: { searchParams: SearchParams }) {
  try {
    const [books, categories] = await Promise.all([
      searchBooks({
        query: searchParams.query || '',
        categoryId: searchParams.category,
        page: parseInt(searchParams.page || '1'),
        limit: 12,
        sortBy: searchParams.sortBy || 'title'
      }),
      getCategories()
    ])

    return (
      <div className="space-y-6">
        <BookFilters 
          categories={categories}
          initialFilters={{
            query: searchParams.query || '',
            categoryId: searchParams.category || '',
            sortBy: searchParams.sortBy || 'title'
          }}
        />
        
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            {books.total} book{books.total !== 1 ? 's' : ''} found
          </p>
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              onClick={() => window.location.href = "/dashboard/admin/books/bulk-import"}
            >
              Bulk Import
            </Button>
            <Button onClick={() => window.location.href = "/dashboard/admin/books/new"}>
              Add New Book
            </Button>
          </div>
        </div>

        <BookGrid books={books.books} showActions={true} />
        
        {books.totalPages > 1 && (
          <div className="flex justify-center">
            <div className="flex space-x-2">
              {Array.from({ length: books.totalPages }, (_, i) => i + 1).map((page) => (
                <Button
                  key={page}
                  variant={page === books.page ? "default" : "outline"}
                  size="sm"
                  onClick={() => {
                    const url = new URL(window.location.href)
                    url.searchParams.set('page', page.toString())
                    window.location.href = url.toString()
                  }}
                >
                  {page}
                </Button>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  } catch (error) {
    console.error('Failed to load books:', error)
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Unable to load books at this time.</p>
      </div>
    )
  }
}

export default async function AdminBooksPage({
  searchParams
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect('/auth/signin?callbackUrl=/dashboard/admin/books')
  }

  const user = session.user as any
  
  // Check if user has admin/librarian role
  if (!['ADMIN', 'LIBRARIAN'].includes(user.role)) {
    redirect('/dashboard')
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Book Management</h1>
          <p className="text-muted-foreground">
            Manage your library's book collection
          </p>
        </div>
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            onClick={() => window.location.href = "/dashboard/admin/categories"}
          >
            Manage Categories
          </Button>
          <Button onClick={() => window.location.href = "/dashboard/admin/books/new"}>
            Add New Book
          </Button>
        </div>
      </div>

      {/* Books Management */}
      <Card>
        <CardHeader>
          <CardTitle>Library Books</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<SectionLoading message="Loading books..." />}>
            <BooksSection searchParams={searchParams} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}
