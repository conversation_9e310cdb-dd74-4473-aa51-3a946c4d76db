import { Suspense } from "react"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { BookForm } from "@/components/forms/book-form"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { SectionLoading } from "@/components/ui/loading"
import { getCategories } from "@/lib/actions/categories"

async function NewBookSection() {
  try {
    const categories = await getCategories()
    
    return (
      <BookForm 
        mode="create"
        categories={categories}
        onSuccess={() => window.location.href = "/dashboard/admin/books"}
        onCancel={() => window.location.href = "/dashboard/admin/books"}
      />
    )
  } catch (error) {
    console.error('Failed to load categories:', error)
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Unable to load form at this time.</p>
      </div>
    )
  }
}

export default async function NewBookPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect('/auth/signin?callbackUrl=/dashboard/admin/books/new')
  }

  const user = session.user as any
  
  // Check if user has admin/librarian role
  if (!['ADMIN', 'LIBRARIAN'].includes(user.role)) {
    redirect('/dashboard')
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Add New Book</h1>
        <p className="text-muted-foreground">
          Add a new book to your library collection
        </p>
      </div>

      {/* Book Form */}
      <Card>
        <CardHeader>
          <CardTitle>Book Information</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<SectionLoading message="Loading form..." />}>
            <NewBookSection />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}
