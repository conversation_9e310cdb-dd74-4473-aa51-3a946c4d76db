'use server'

import { revalidatePath } from 'next/cache'
import { prisma } from '@/lib/prisma'
import { addDaysToDate, isOverdue } from '@/lib/utils'
import { 
  BorrowRequestForm, 
  ActionResponse, 
  BorrowRequestWithDetails, 
  BorrowWithDetails 
} from '@/types'
import { createNotification } from './notifications'

// Create borrow request
export async function createBorrowRequest(
  userId: string, 
  data: BorrowRequestForm
): Promise<ActionResponse> {
  try {
    // Check if copy is available
    const copy = await prisma.bookCopy.findUnique({
      where: { id: data.copyId },
      include: { book: true }
    })

    if (!copy) {
      return {
        success: false,
        error: 'Book copy not found'
      }
    }

    if (copy.status !== 'AVAILABLE') {
      return {
        success: false,
        error: 'Book copy is not available for borrowing'
      }
    }

    // Check if user already has a pending request for this copy
    const existingRequest = await prisma.borrowRequest.findFirst({
      where: {
        userId,
        copyId: data.copyId,
        status: 'PENDING'
      }
    })

    if (existingRequest) {
      return {
        success: false,
        error: 'You already have a pending request for this book'
      }
    }

    // Check if user has too many active borrows (limit to 5)
    const activeBorrowsCount = await prisma.borrow.count({
      where: {
        userId,
        status: {
          in: ['ACTIVE', 'OVERDUE']
        }
      }
    })

    if (activeBorrowsCount >= 5) {
      return {
        success: false,
        error: 'You have reached the maximum limit of 5 active borrows'
      }
    }

    // Create borrow request
    const request = await prisma.borrowRequest.create({
      data: {
        userId,
        copyId: data.copyId,
        requestedReturnDate: data.requestedReturnDate
      }
    })

    // Update copy status to reserved
    await prisma.bookCopy.update({
      where: { id: data.copyId },
      data: { status: 'RESERVED' }
    })

    // Create notification for admins/librarians
    const admins = await prisma.user.findMany({
      where: {
        role: {
          in: ['ADMIN', 'LIBRARIAN']
        }
      }
    })

    for (const admin of admins) {
      await createNotification({
        userId: admin.id,
        type: 'SYSTEM',
        title: 'New Borrow Request',
        message: `A new borrow request has been submitted for "${copy.book.title}"`
      })
    }

    revalidatePath('/my-requests')
    revalidatePath('/admin/requests')

    return {
      success: true,
      data: request,
      message: 'Borrow request submitted successfully'
    }
  } catch (error) {
    console.error('Error creating borrow request:', error)
    return {
      success: false,
      error: 'Failed to create borrow request'
    }
  }
}

// Get user's borrow requests
export async function getUserBorrowRequests(userId: string): Promise<BorrowRequestWithDetails[]> {
  try {
    const requests = await prisma.borrowRequest.findMany({
      where: { userId },
      include: {
        user: true,
        copy: {
          include: {
            book: true
          }
        },
        processedByUser: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return requests as BorrowRequestWithDetails[]
  } catch (error) {
    console.error('Error getting user borrow requests:', error)
    return []
  }
}

// Get all pending borrow requests (admin/librarian)
export async function getPendingBorrowRequests(): Promise<BorrowRequestWithDetails[]> {
  try {
    const requests = await prisma.borrowRequest.findMany({
      where: { status: 'PENDING' },
      include: {
        user: true,
        copy: {
          include: {
            book: true
          }
        },
        processedByUser: true
      },
      orderBy: {
        createdAt: 'asc'
      }
    })

    return requests as BorrowRequestWithDetails[]
  } catch (error) {
    console.error('Error getting pending borrow requests:', error)
    return []
  }
}

// Approve borrow request
export async function approveBorrowRequest(
  requestId: string, 
  processedBy: string,
  adminNotes?: string
): Promise<ActionResponse> {
  try {
    const request = await prisma.borrowRequest.findUnique({
      where: { id: requestId },
      include: {
        user: true,
        copy: {
          include: { book: true }
        }
      }
    })

    if (!request) {
      return {
        success: false,
        error: 'Request not found'
      }
    }

    if (request.status !== 'PENDING') {
      return {
        success: false,
        error: 'Request has already been processed'
      }
    }

    // Calculate due date (14 days from now)
    const dueDate = addDaysToDate(new Date(), 14)

    // Start transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update request status
      await tx.borrowRequest.update({
        where: { id: requestId },
        data: {
          status: 'APPROVED',
          processedBy,
          processedAt: new Date(),
          adminNotes
        }
      })

      // Create borrow record
      const borrow = await tx.borrow.create({
        data: {
          userId: request.userId,
          copyId: request.copyId,
          requestId: requestId,
          dueDate
        }
      })

      // Update copy status to borrowed
      await tx.bookCopy.update({
        where: { id: request.copyId },
        data: { status: 'BORROWED' }
      })

      return borrow
    })

    // Create notification for user
    await createNotification({
      userId: request.userId,
      type: 'APPROVAL',
      title: 'Borrow Request Approved',
      message: `Your request for "${request.copy.book.title}" has been approved. Due date: ${dueDate.toDateString()}`
    })

    revalidatePath('/admin/requests')
    revalidatePath('/admin/borrows')
    revalidatePath('/my-requests')
    revalidatePath('/my-books')

    return {
      success: true,
      data: result,
      message: 'Borrow request approved successfully'
    }
  } catch (error) {
    console.error('Error approving borrow request:', error)
    return {
      success: false,
      error: 'Failed to approve borrow request'
    }
  }
}

// Reject borrow request
export async function rejectBorrowRequest(
  requestId: string, 
  processedBy: string,
  adminNotes: string
): Promise<ActionResponse> {
  try {
    const request = await prisma.borrowRequest.findUnique({
      where: { id: requestId },
      include: {
        user: true,
        copy: {
          include: { book: true }
        }
      }
    })

    if (!request) {
      return {
        success: false,
        error: 'Request not found'
      }
    }

    if (request.status !== 'PENDING') {
      return {
        success: false,
        error: 'Request has already been processed'
      }
    }

    // Start transaction
    await prisma.$transaction(async (tx) => {
      // Update request status
      await tx.borrowRequest.update({
        where: { id: requestId },
        data: {
          status: 'REJECTED',
          processedBy,
          processedAt: new Date(),
          adminNotes
        }
      })

      // Update copy status back to available
      await tx.bookCopy.update({
        where: { id: request.copyId },
        data: { status: 'AVAILABLE' }
      })
    })

    // Create notification for user
    await createNotification({
      userId: request.userId,
      type: 'REJECTION',
      title: 'Borrow Request Rejected',
      message: `Your request for "${request.copy.book.title}" has been rejected. Reason: ${adminNotes}`
    })

    revalidatePath('/admin/requests')
    revalidatePath('/my-requests')

    return {
      success: true,
      message: 'Borrow request rejected successfully'
    }
  } catch (error) {
    console.error('Error rejecting borrow request:', error)
    return {
      success: false,
      error: 'Failed to reject borrow request'
    }
  }
}

// Get user's active borrows
export async function getUserActiveBorrows(userId: string): Promise<BorrowWithDetails[]> {
  try {
    const borrows = await prisma.borrow.findMany({
      where: {
        userId,
        status: {
          in: ['ACTIVE', 'OVERDUE']
        }
      },
      include: {
        user: true,
        copy: {
          include: {
            book: true
          }
        },
        request: true
      },
      orderBy: {
        dueDate: 'asc'
      }
    })

    return borrows as BorrowWithDetails[]
  } catch (error) {
    console.error('Error getting user active borrows:', error)
    return []
  }
}

// Get all active borrows (admin/librarian)
export async function getAllActiveBorrows(): Promise<BorrowWithDetails[]> {
  try {
    const borrows = await prisma.borrow.findMany({
      where: {
        status: {
          in: ['ACTIVE', 'OVERDUE']
        }
      },
      include: {
        user: true,
        copy: {
          include: {
            book: true
          }
        },
        request: true
      },
      orderBy: {
        dueDate: 'asc'
      }
    })

    return borrows as BorrowWithDetails[]
  } catch (error) {
    console.error('Error getting all active borrows:', error)
    return []
  }
}

// Return book
export async function returnBook(borrowId: string): Promise<ActionResponse> {
  try {
    const borrow = await prisma.borrow.findUnique({
      where: { id: borrowId },
      include: {
        copy: {
          include: { book: true }
        },
        user: true
      }
    })

    if (!borrow) {
      return {
        success: false,
        error: 'Borrow record not found'
      }
    }

    if (borrow.status === 'RETURNED') {
      return {
        success: false,
        error: 'Book has already been returned'
      }
    }

    // Start transaction
    await prisma.$transaction(async (tx) => {
      // Update borrow record
      await tx.borrow.update({
        where: { id: borrowId },
        data: {
          status: 'RETURNED',
          returnDate: new Date()
        }
      })

      // Update copy status to available
      await tx.bookCopy.update({
        where: { id: borrow.copyId },
        data: { status: 'AVAILABLE' }
      })
    })

    revalidatePath('/admin/borrows')
    revalidatePath('/my-books')

    return {
      success: true,
      message: 'Book returned successfully'
    }
  } catch (error) {
    console.error('Error returning book:', error)
    return {
      success: false,
      error: 'Failed to return book'
    }
  }
}

// Update overdue books (to be called by cron job)
export async function updateOverdueBooks(): Promise<ActionResponse> {
  try {
    const overdueBooks = await prisma.borrow.findMany({
      where: {
        status: 'ACTIVE',
        dueDate: {
          lt: new Date()
        }
      },
      include: {
        user: true,
        copy: {
          include: { book: true }
        }
      }
    })

    for (const borrow of overdueBooks) {
      // Update status to overdue
      await prisma.borrow.update({
        where: { id: borrow.id },
        data: { status: 'OVERDUE' }
      })

      // Create overdue notification
      await createNotification({
        userId: borrow.userId,
        type: 'OVERDUE',
        title: 'Overdue Book',
        message: `"${borrow.copy.book.title}" is overdue. Please return it as soon as possible.`
      })
    }

    return {
      success: true,
      message: `Updated ${overdueBooks.length} overdue books`
    }
  } catch (error) {
    console.error('Error updating overdue books:', error)
    return {
      success: false,
      error: 'Failed to update overdue books'
    }
  }
}

// Get overdue books
export async function getOverdueBooks(): Promise<BorrowWithDetails[]> {
  try {
    const overdueBooks = await prisma.borrow.findMany({
      where: {
        status: 'OVERDUE'
      },
      include: {
        user: true,
        copy: {
          include: {
            book: true
          }
        },
        request: true
      },
      orderBy: {
        dueDate: 'asc'
      }
    })

    return overdueBooks as BorrowWithDetails[]
  } catch (error) {
    console.error('Error getting overdue books:', error)
    return []
  }
} 