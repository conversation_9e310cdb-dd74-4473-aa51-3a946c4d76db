'use server'

import { revalidatePath } from 'next/cache'
import { prisma } from '@/lib/prisma'
import { generateCopyCode } from '@/lib/utils'
import { 
  BookSearchParams, 
  BookSearchResult, 
  CreateBookForm, 
  ActionResponse,
  BookWithDetails
} from '@/types'

// Get all books with search and filtering
export async function searchBooks(params: BookSearchParams): Promise<BookSearchResult> {
  const {
    query = '',
    categories = [],
    author = '',
    language = '',
    hasDigitalCopy,
    availability = 'all',
    sortBy = 'title',
    sortOrder = 'asc',
    page = 1,
    limit = 12
  } = params

  const skip = (page - 1) * limit

  // Build where clause
  const where: any = {}

  if (query) {
    where.OR = [
      { title: { contains: query, mode: 'insensitive' } },
      { author: { contains: query, mode: 'insensitive' } },
      { description: { contains: query, mode: 'insensitive' } },
      { isbn: { contains: query, mode: 'insensitive' } }
    ]
  }

  if (author) {
    where.author = { contains: author, mode: 'insensitive' }
  }

  if (language) {
    where.language = language
  }

  if (hasDigitalCopy !== undefined) {
    where.hasDigitalCopy = hasDigitalCopy
  }

  if (categories.length > 0) {
    where.categories = {
      some: {
        categoryId: { in: categories }
      }
    }
  }

  if (availability === 'available') {
    where.copies = {
      some: {
        status: 'AVAILABLE'
      }
    }
  }

  // Build order by clause
  const orderBy: any = {}
  if (sortBy === 'title') {
    orderBy.title = sortOrder
  } else if (sortBy === 'author') {
    orderBy.author = sortOrder
  } else if (sortBy === 'year') {
    orderBy.publicationYear = sortOrder
  } else if (sortBy === 'created') {
    orderBy.createdAt = sortOrder
  }

  try {
    const [books, totalCount] = await Promise.all([
      prisma.book.findMany({
        where,
        include: {
          categories: {
            include: {
              category: true
            }
          },
          copies: true,
          _count: {
            select: {
              copies: true,
              downloadLogs: true
            }
          }
        },
        orderBy,
        skip,
        take: limit
      }),
      prisma.book.count({ where })
    ])

    const totalPages = Math.ceil(totalCount / limit)

    const booksWithDetails: BookWithDetails[] = books.map(book => ({
      ...book,
      categories: book.categories.map(bc => bc.category),
      hasDigitalCopy: book.hasDigitalCopy,
      _count: {
        copies: book.copies.length,
        downloadLogs: 0
      }
    }))

    return {
      books: booksWithDetails,
      totalCount,
      currentPage: page,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1
    }
  } catch (error) {
    console.error('Error searching books:', error)
    return {
      books: [],
      totalCount: 0,
      currentPage: 1,
      totalPages: 0,
      hasNextPage: false,
      hasPrevPage: false
    }
  }
}

// Get book by ID
export async function getBookById(id: string): Promise<BookWithDetails | null> {
  try {
    const book = await prisma.book.findUnique({
      where: { id },
      include: {
        categories: {
          include: {
            category: true
          }
        },
        copies: {
          orderBy: {
            copyCode: 'asc'
          }
        },
        _count: {
          select: {
            copies: true,
            downloadLogs: true
          }
        }
      }
    })

    if (!book) return null

    const bookWithDetails: BookWithDetails = {
      ...book,
      categories: book.categories.map(bc => bc.category),
      hasDigitalCopy: book.hasDigitalCopy
    }

    return bookWithDetails
  } catch (error) {
    console.error('Error getting book:', error)
    return null
  }
}

// Create new book
export async function createBook(data: CreateBookForm): Promise<ActionResponse> {
  try {
    const book = await prisma.book.create({
      data: {
        title: data.title,
        author: data.author,
        isbn: data.isbn,
        description: data.description,
        publisher: data.publisher,
        publicationYear: data.publicationYear,
        pages: data.pages,
        language: data.language,
        coverImageUrl: data.coverImageUrl,
        pdfUrl: data.pdfUrl,
        hasDigitalCopy: data.hasDigitalCopy,
        categories: {
          create: data.categories.map(categoryId => ({
            categoryId
          }))
        },
        copies: {
          create: data.copies.map(copy => ({
            copyCode: copy.copyCode,
            condition: copy.condition,
            location: copy.location
          }))
        }
      }
    })

    revalidatePath('/books')
    revalidatePath('/admin/books')

    return {
      success: true,
      data: book,
      message: 'Book created successfully'
    }
  } catch (error: any) {
    console.error('Error creating book:', error)
    
    if (error.code === 'P2002') {
      return {
        success: false,
        error: 'A book with this ISBN already exists'
      }
    }

    return {
      success: false,
      error: 'Failed to create book'
    }
  }
}

// Update book
export async function updateBook(id: string, data: Partial<CreateBookForm>): Promise<ActionResponse> {
  try {
    const updateData: any = {
      title: data.title,
      author: data.author,
      isbn: data.isbn,
      description: data.description,
      publisher: data.publisher,
      publicationYear: data.publicationYear,
      pages: data.pages,
      language: data.language,
      coverImageUrl: data.coverImageUrl,
      pdfUrl: data.pdfUrl,
      hasDigitalCopy: data.hasDigitalCopy,
    }

    // Handle categories update if provided
    if (data.categories) {
      updateData.categories = {
        deleteMany: {},
        create: data.categories.map(categoryId => ({
          categoryId
        }))
      }
    }

    const book = await prisma.book.update({
      where: { id },
      data: updateData
    })

    revalidatePath('/books')
    revalidatePath(`/books/${id}`)
    revalidatePath('/admin/books')

    return {
      success: true,
      data: book,
      message: 'Book updated successfully'
    }
  } catch (error: any) {
    console.error('Error updating book:', error)
    
    if (error.code === 'P2002') {
      return {
        success: false,
        error: 'A book with this ISBN already exists'
      }
    }

    return {
      success: false,
      error: 'Failed to update book'
    }
  }
}

// Delete book
export async function deleteBook(id: string): Promise<ActionResponse> {
  try {
    // Check if book has active borrows
    const activeBorrows = await prisma.borrow.findFirst({
      where: {
        copy: {
          bookId: id
        },
        status: {
          in: ['ACTIVE', 'OVERDUE']
        }
      }
    })

    if (activeBorrows) {
      return {
        success: false,
        error: 'Cannot delete book with active borrows'
      }
    }

    await prisma.book.delete({
      where: { id }
    })

    revalidatePath('/books')
    revalidatePath('/admin/books')

    return {
      success: true,
      message: 'Book deleted successfully'
    }
  } catch (error) {
    console.error('Error deleting book:', error)
    return {
      success: false,
      error: 'Failed to delete book'
    }
  }
}

// Add book copy
export async function addBookCopy(bookId: string, copyData: {
  condition: string
  location?: string
}): Promise<ActionResponse> {
  try {
    const book = await prisma.book.findUnique({
      where: { id: bookId },
      select: { title: true, _count: { select: { copies: true } } }
    })

    if (!book) {
      return {
        success: false,
        error: 'Book not found'
      }
    }

    const copyCode = generateCopyCode(book.title, book._count.copies + 1)

    const copy = await prisma.bookCopy.create({
      data: {
        bookId,
        copyCode,
        condition: copyData.condition as any,
        location: copyData.location
      }
    })

    revalidatePath(`/books/${bookId}`)
    revalidatePath('/admin/books')

    return {
      success: true,
      data: copy,
      message: 'Book copy added successfully'
    }
  } catch (error) {
    console.error('Error adding book copy:', error)
    return {
      success: false,
      error: 'Failed to add book copy'
    }
  }
}

// Update book copy
export async function updateBookCopy(copyId: string, data: {
  condition?: string
  location?: string
  status?: string
}): Promise<ActionResponse> {
  try {
    const copy = await prisma.bookCopy.update({
      where: { id: copyId },
      data: {
        condition: data.condition as any,
        location: data.location,
        status: data.status as any
      }
    })

    revalidatePath('/admin/books')

    return {
      success: true,
      data: copy,
      message: 'Book copy updated successfully'
    }
  } catch (error) {
    console.error('Error updating book copy:', error)
    return {
      success: false,
      error: 'Failed to update book copy'
    }
  }
}

// Get books by category
export async function getBooksByCategory(
  categorySlug: string,
  options: {
    page?: number
    limit?: number
    sortBy?: string
  } = {}
): Promise<{
  books: BookWithDetails[]
  totalCount: number
  currentPage: number
  totalPages: number
}> {
  try {
    const { page = 1, limit = 20, sortBy = 'title' } = options
    const skip = (page - 1) * limit

    // Build sort order
    let orderBy: any = { title: 'asc' }
    switch (sortBy) {
      case 'title_desc':
        orderBy = { title: 'desc' }
        break
      case 'author':
        orderBy = { author: 'asc' }
        break
      case 'year':
        orderBy = { publicationYear: 'desc' }
        break
      case 'year_desc':
        orderBy = { publicationYear: 'asc' }
        break
    }

    const where = {
      categories: {
        some: {
          category: {
            slug: categorySlug
          }
        }
      }
    }

    const [books, totalCount] = await Promise.all([
      prisma.book.findMany({
        where,
        include: {
          categories: {
            include: {
              category: true
            }
          },
          copies: true
        },
        orderBy,
        skip,
        take: limit
      }),
      prisma.book.count({ where })
    ])

    const booksWithDetails: BookWithDetails[] = books.map(book => ({
      ...book,
      categories: book.categories.map(bc => bc.category),
      hasDigitalCopy: book.hasDigitalCopy,
      _count: {
        copies: book.copies.length,
        downloadLogs: 0
      }
    }))

    return {
      books: booksWithDetails,
      totalCount,
      currentPage: page,
      totalPages: Math.ceil(totalCount / limit)
    }
  } catch (error) {
    console.error('Error getting books by category:', error)
    return {
      books: [],
      totalCount: 0,
      currentPage: 1,
      totalPages: 0
    }
  }
}

// Get featured books (for homepage)
export async function getFeaturedBooks(limit: number = 8): Promise<BookWithDetails[]> {
  try {
    const books = await prisma.book.findMany({
      include: {
        categories: {
          include: {
            category: true
          }
        },
        copies: true,
        _count: {
          select: {
            copies: true,
            downloadLogs: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit
    })

    const booksWithDetails: BookWithDetails[] = books.map(book => ({
      ...book,
      categories: book.categories.map(bc => bc.category),
      hasDigitalCopy: book.hasDigitalCopy,
      _count: {
        copies: book.copies.length,
        downloadLogs: 0
      }
    }))

    return booksWithDetails
  } catch (error) {
    console.error('Error getting featured books:', error)
    return []
  }
} 