'use server'

import { revalidatePath } from 'next/cache'
import { prisma } from '@/lib/prisma'
import { generateSlug } from '@/lib/utils'
import { Category, ActionResponse } from '@/types'

// Get all categories with book counts
export async function getCategories(): Promise<(Category & { _count: { books: number } })[]> {
  try {
    const categories = await prisma.category.findMany({
      include: {
        _count: {
          select: {
            books: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    })

    return categories
  } catch (error) {
    console.error('Error getting categories:', error)
    return []
  }
}

// Get category by ID
export async function getCategoryById(id: string): Promise<Category | null> {
  try {
    const category = await prisma.category.findUnique({
      where: { id }
    })

    return category
  } catch (error) {
    console.error('Error getting category:', error)
    return null
  }
}

// Get category by slug
export async function getCategoryBySlug(slug: string): Promise<Category | null> {
  try {
    const category = await prisma.category.findUnique({
      where: { slug }
    })

    return category
  } catch (error) {
    console.error('Error getting category by slug:', error)
    return null
  }
}

// Create new category
export async function createCategory(data: {
  name: string
  description?: string
}): Promise<ActionResponse> {
  try {
    const slug = generateSlug(data.name)

    const category = await prisma.category.create({
      data: {
        name: data.name,
        description: data.description,
        slug
      }
    })

    revalidatePath('/admin/categories')
    revalidatePath('/books')

    return {
      success: true,
      data: category,
      message: 'Category created successfully'
    }
  } catch (error: any) {
    console.error('Error creating category:', error)
    
    if (error.code === 'P2002') {
      return {
        success: false,
        error: 'A category with this name already exists'
      }
    }

    return {
      success: false,
      error: 'Failed to create category'
    }
  }
}

// Update category
export async function updateCategory(id: string, data: {
  name?: string
  description?: string
}): Promise<ActionResponse> {
  try {
    const updateData: any = {
      name: data.name,
      description: data.description
    }

    // Generate new slug if name is being updated
    if (data.name) {
      updateData.slug = generateSlug(data.name)
    }

    const category = await prisma.category.update({
      where: { id },
      data: updateData
    })

    revalidatePath('/admin/categories')
    revalidatePath('/books')

    return {
      success: true,
      data: category,
      message: 'Category updated successfully'
    }
  } catch (error: any) {
    console.error('Error updating category:', error)
    
    if (error.code === 'P2002') {
      return {
        success: false,
        error: 'A category with this name already exists'
      }
    }

    return {
      success: false,
      error: 'Failed to update category'
    }
  }
}

// Delete category
export async function deleteCategory(id: string): Promise<ActionResponse> {
  try {
    // Check if category has books
    const booksCount = await prisma.bookCategory.count({
      where: { categoryId: id }
    })

    if (booksCount > 0) {
      return {
        success: false,
        error: 'Cannot delete category that has books assigned to it'
      }
    }

    await prisma.category.delete({
      where: { id }
    })

    revalidatePath('/admin/categories')
    revalidatePath('/books')

    return {
      success: true,
      message: 'Category deleted successfully'
    }
  } catch (error) {
    console.error('Error deleting category:', error)
    return {
      success: false,
      error: 'Failed to delete category'
    }
  }
}