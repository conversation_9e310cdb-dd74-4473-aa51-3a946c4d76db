'use server'

import { revalidatePath } from 'next/cache'
import { prisma } from '@/lib/prisma'
import { hashPassword } from '@/lib/utils'
import { CreateUserForm, ActionResponse, User, UserWithStats } from '@/types'

// Register new user
export async function registerUser(data: CreateUserForm): Promise<ActionResponse> {
  try {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email }
    })

    if (existingUser) {
      return {
        success: false,
        error: 'User with this email already exists'
      }
    }

    // Hash password
    const hashedPassword = await hashPassword(data.password)

    // Create user
    const user = await prisma.user.create({
      data: {
        name: data.name,
        email: data.email,
        password: hashedPassword,
        phone: data.phone,
        address: data.address,
        role: data.role || 'USER'
      }
    })

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user

    revalidatePath('/admin/users')

    return {
      success: true,
      data: userWithoutPassword,
      message: 'User registered successfully'
    }
  } catch (error) {
    console.error('Error registering user:', error)
    return {
      success: false,
      error: 'Failed to register user'
    }
  }
}

// Get user by ID
export async function getUserById(id: string): Promise<User | null> {
  try {
    const user = await prisma.user.findUnique({
      where: { id }
    })

    if (user) {
      // Remove password from response
      const { password: _, ...userWithoutPassword } = user
      return userWithoutPassword as User
    }

    return null
  } catch (error) {
    console.error('Error getting user:', error)
    return null
  }
}

// Update user profile
export async function updateUserProfile(id: string, data: {
  name?: string
  phone?: string
  address?: string
}): Promise<ActionResponse> {
  try {
    const user = await prisma.user.update({
      where: { id },
      data: {
        name: data.name,
        phone: data.phone,
        address: data.address
      }
    })

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user

    revalidatePath('/profile')

    return {
      success: true,
      data: userWithoutPassword,
      message: 'Profile updated successfully'
    }
  } catch (error) {
    console.error('Error updating profile:', error)
    return {
      success: false,
      error: 'Failed to update profile'
    }
  }
}

// Update user password
export async function updateUserPassword(id: string, newPassword: string): Promise<ActionResponse> {
  try {
    const hashedPassword = await hashPassword(newPassword)

    await prisma.user.update({
      where: { id },
      data: {
        password: hashedPassword
      }
    })

    return {
      success: true,
      message: 'Password updated successfully'
    }
  } catch (error) {
    console.error('Error updating password:', error)
    return {
      success: false,
      error: 'Failed to update password'
    }
  }
}

// Get all users (admin only)
export async function getUsers(): Promise<UserWithStats[]> {
  try {
    const users = await prisma.user.findMany({
      include: {
        _count: {
          select: {
            borrows: true,
            borrowRequests: true,
            notifications: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Remove passwords from response
    return users.map(user => {
      const { password: _, ...userWithoutPassword } = user
      return userWithoutPassword as UserWithStats
    })
  } catch (error) {
    console.error('Error getting users:', error)
    return []
  }
}

// Update user role (admin only)
export async function updateUserRole(id: string, role: string): Promise<ActionResponse> {
  try {
    const user = await prisma.user.update({
      where: { id },
      data: { role: role as any }
    })

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user

    revalidatePath('/admin/users')

    return {
      success: true,
      data: userWithoutPassword,
      message: 'User role updated successfully'
    }
  } catch (error) {
    console.error('Error updating user role:', error)
    return {
      success: false,
      error: 'Failed to update user role'
    }
  }
}

// Delete user (admin only)
export async function deleteUser(id: string): Promise<ActionResponse> {
  try {
    // Check if user has active borrows
    const activeBorrows = await prisma.borrow.findFirst({
      where: {
        userId: id,
        status: {
          in: ['ACTIVE', 'OVERDUE']
        }
      }
    })

    if (activeBorrows) {
      return {
        success: false,
        error: 'Cannot delete user with active borrows'
      }
    }

    await prisma.user.delete({
      where: { id }
    })

    revalidatePath('/admin/users')

    return {
      success: true,
      message: 'User deleted successfully'
    }
  } catch (error) {
    console.error('Error deleting user:', error)
    return {
      success: false,
      error: 'Failed to delete user'
    }
  }
}

// Get user statistics
export async function getUserStats(userId: string): Promise<{
  activeBorrows: number
  totalBorrows: number
  overdueBooks: number
  pendingRequests: number
} | null> {
  try {
    const [activeBorrows, totalBorrows, overdueBooks, pendingRequests] = await Promise.all([
      prisma.borrow.count({
        where: {
          userId,
          status: 'ACTIVE'
        }
      }),
      prisma.borrow.count({
        where: { userId }
      }),
      prisma.borrow.count({
        where: {
          userId,
          status: 'OVERDUE'
        }
      }),
      prisma.borrowRequest.count({
        where: {
          userId,
          status: 'PENDING'
        }
      })
    ])

    return {
      activeBorrows,
      totalBorrows,
      overdueBooks,
      pendingRequests
    }
  } catch (error) {
    console.error('Error getting user stats:', error)
    return null
  }
} 