'use server'

import { revalidate<PERSON>ath } from 'next/cache'
import { prisma } from '@/lib/prisma'
import { NotificationPayload, ActionResponse, Notification } from '@/types'

// Create notification
export async function createNotification(data: NotificationPayload): Promise<ActionResponse> {
  try {
    const notification = await prisma.notification.create({
      data: {
        userId: data.userId,
        type: data.type,
        title: data.title,
        message: data.message
      }
    })

    revalidatePath('/notifications')

    return {
      success: true,
      data: notification,
      message: 'Notification created successfully'
    }
  } catch (error) {
    console.error('Error creating notification:', error)
    return {
      success: false,
      error: 'Failed to create notification'
    }
  }
}

// Get user notifications
export async function getUserNotifications(userId: string): Promise<Notification[]> {
  try {
    const notifications = await prisma.notification.findMany({
      where: { userId },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return notifications
  } catch (error) {
    console.error('Error getting user notifications:', error)
    return []
  }
}

// Get unread notifications count
export async function getUnreadNotificationsCount(userId: string): Promise<number> {
  try {
    const count = await prisma.notification.count({
      where: {
        userId,
        read: false
      }
    })

    return count
  } catch (error) {
    console.error('Error getting unread notifications count:', error)
    return 0
  }
}

// Mark notification as read
export async function markNotificationAsRead(notificationId: string): Promise<ActionResponse> {
  try {
    await prisma.notification.update({
      where: { id: notificationId },
      data: { read: true }
    })

    revalidatePath('/notifications')

    return {
      success: true,
      message: 'Notification marked as read'
    }
  } catch (error) {
    console.error('Error marking notification as read:', error)
    return {
      success: false,
      error: 'Failed to mark notification as read'
    }
  }
}

// Mark all notifications as read
export async function markAllNotificationsAsRead(userId: string): Promise<ActionResponse> {
  try {
    await prisma.notification.updateMany({
      where: {
        userId,
        read: false
      },
      data: { read: true }
    })

    revalidatePath('/notifications')

    return {
      success: true,
      message: 'All notifications marked as read'
    }
  } catch (error) {
    console.error('Error marking all notifications as read:', error)
    return {
      success: false,
      error: 'Failed to mark all notifications as read'
    }
  }
}

// Delete notification
export async function deleteNotification(notificationId: string): Promise<ActionResponse> {
  try {
    await prisma.notification.delete({
      where: { id: notificationId }
    })

    revalidatePath('/notifications')

    return {
      success: true,
      message: 'Notification deleted successfully'
    }
  } catch (error) {
    console.error('Error deleting notification:', error)
    return {
      success: false,
      error: 'Failed to delete notification'
    }
  }
}

// Create bulk notifications (for system announcements)
export async function createBulkNotifications(
  userIds: string[],
  data: Omit<NotificationPayload, 'userId'>
): Promise<ActionResponse> {
  try {
    const notifications = userIds.map(userId => ({
      userId,
      type: data.type,
      title: data.title,
      message: data.message
    }))

    await prisma.notification.createMany({
      data: notifications
    })

    revalidatePath('/notifications')

    return {
      success: true,
      message: `Created ${notifications.length} notifications`
    }
  } catch (error) {
    console.error('Error creating bulk notifications:', error)
    return {
      success: false,
      error: 'Failed to create bulk notifications'
    }
  }
}

// Send notification to all users with specific role
export async function notifyUsersByRole(
  roles: string[],
  data: Omit<NotificationPayload, 'userId'>
): Promise<ActionResponse> {
  try {
    const users = await prisma.user.findMany({
      where: {
        role: {
          in: roles as any[]
        }
      },
      select: { id: true }
    })

    const userIds = users.map(user => user.id)
    
    if (userIds.length === 0) {
      return {
        success: true,
        message: 'No users found with specified roles'
      }
    }

    return await createBulkNotifications(userIds, data)
  } catch (error) {
    console.error('Error notifying users by role:', error)
    return {
      success: false,
      error: 'Failed to notify users by role'
    }
  }
} 