'use server'

import { prisma } from '@/lib/prisma'
import { DashboardStats } from '@/types'

// Get admin dashboard statistics
export async function getAdminDashboardStats(): Promise<DashboardStats> {
  try {
    const [
      totalBooks,
      totalUsers,
      activeBorrows,
      pendingRequests,
      overdueBooks,
      availableCopies
    ] = await Promise.all([
      prisma.book.count(),
      prisma.user.count(),
      prisma.borrow.count({
        where: {
          status: 'ACTIVE'
        }
      }),
      prisma.borrowRequest.count({
        where: {
          status: 'PENDING'
        }
      }),
      prisma.borrow.count({
        where: {
          status: 'OVERDUE'
        }
      }),
      prisma.bookCopy.count({
        where: {
          status: 'AVAILABLE'
        }
      })
    ])

    return {
      totalBooks,
      totalUsers,
      activeBorrows,
      pendingRequests,
      overdueBooks,
      availableCopies
    }
  } catch (error) {
    console.error('Error getting admin dashboard stats:', error)
    return {
      totalBooks: 0,
      totalUsers: 0,
      activeBorrows: 0,
      pendingRequests: 0,
      overdueBooks: 0,
      availableCopies: 0
    }
  }
}

// Get popular books (most borrowed)
export async function getPopularBooks(limit: number = 10) {
  try {
    const popularBooks = await prisma.book.findMany({
      include: {
        copies: true,
        categories: {
          include: {
            category: true
          }
        },
        _count: {
          select: {
            downloadLogs: true
          }
        }
      },
      orderBy: {
        downloadLogs: {
          _count: 'desc'
        }
      },
      take: limit
    })

    return popularBooks
  } catch (error) {
    console.error('Error getting popular books:', error)
    return []
  }
}

// Get recent activities (for admin dashboard)
export async function getRecentActivities(limit: number = 10) {
  try {
    const [recentBorrows, recentRequests, recentReturns] = await Promise.all([
      // Recent borrows
      prisma.borrow.findMany({
        where: {
          status: 'ACTIVE'
        },
        include: {
          user: {
            select: { name: true, email: true }
          },
          copy: {
            include: {
              book: {
                select: { title: true, author: true }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: Math.ceil(limit / 3)
      }),
      // Recent requests
      prisma.borrowRequest.findMany({
        where: {
          status: 'PENDING'
        },
        include: {
          user: {
            select: { name: true, email: true }
          },
          copy: {
            include: {
              book: {
                select: { title: true, author: true }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: Math.ceil(limit / 3)
      }),
      // Recent returns
      prisma.borrow.findMany({
        where: {
          status: 'RETURNED',
          returnDate: {
            not: null
          }
        },
        include: {
          user: {
            select: { name: true, email: true }
          },
          copy: {
            include: {
              book: {
                select: { title: true, author: true }
              }
            }
          }
        },
        orderBy: {
          returnDate: 'desc'
        },
        take: Math.ceil(limit / 3)
      })
    ])

    // Combine and sort all activities
    const activities = [
      ...recentBorrows.map(borrow => ({
        id: borrow.id,
        type: 'BORROW' as const,
        user: borrow.user,
        book: borrow.copy.book,
        date: borrow.createdAt,
        dueDate: borrow.dueDate
      })),
      ...recentRequests.map(request => ({
        id: request.id,
        type: 'REQUEST' as const,
        user: request.user,
        book: request.copy.book,
        date: request.createdAt
      })),
      ...recentReturns.map(borrow => ({
        id: borrow.id,
        type: 'RETURN' as const,
        user: borrow.user,
        book: borrow.copy.book,
        date: borrow.returnDate!
      }))
    ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

    return activities.slice(0, limit)
  } catch (error) {
    console.error('Error getting recent activities:', error)
    return []
  }
}

// Get borrowing trends (monthly data for the last 12 months)
export async function getBorrowingTrends() {
  try {
    const now = new Date()
    const trends = []

    // Generate last 12 months
    for (let i = 11; i >= 0; i--) {
      const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const nextMonth = new Date(now.getFullYear(), now.getMonth() - i + 1, 1)
      
      const count = await prisma.borrow.count({
        where: {
          createdAt: {
            gte: monthDate,
            lt: nextMonth
          }
        }
      })

      trends.push({
        month: monthDate.toISOString().slice(0, 7), // YYYY-MM format
        count
      })
    }

    return trends
  } catch (error) {
    console.error('Error getting borrowing trends:', error)
    return []
  }
}

// Get category distribution
export async function getCategoryDistribution() {
  try {
    const categories = await prisma.category.findMany({
      include: {
        _count: {
          select: {
            books: true
          }
        }
      },
      orderBy: {
        books: {
          _count: 'desc'
        }
      }
    })

    return categories.map(category => ({
      name: category.name,
      count: category._count.books
    }))
  } catch (error) {
    console.error('Error getting category distribution:', error)
    return []
  }
}

// Get user engagement stats
export async function getUserEngagementStats() {
  try {
    const [
      totalUsers,
      activeUsers,
      usersWithBorrows,
      usersWithRequests,
      totalBorrows
    ] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({
        where: {
          borrows: {
            some: {
              status: {
                in: ['ACTIVE', 'OVERDUE']
              }
            }
          }
        }
      }),
      prisma.user.count({
        where: {
          borrows: {
            some: {}
          }
        }
      }),
      prisma.user.count({
        where: {
          borrowRequests: {
            some: {}
          }
        }
      }),
      prisma.borrow.count()
    ])

    const averageBorrowsPerUser = totalUsers > 0 ? Math.round((totalBorrows / totalUsers) * 100) / 100 : 0

    return {
      totalUsers,
      activeUsers,
      usersWithBorrows,
      usersWithRequests,
      averageBorrowsPerUser
    }
  } catch (error) {
    console.error('Error getting user engagement stats:', error)
    return {
      totalUsers: 0,
      activeUsers: 0,
      usersWithBorrows: 0,
      usersWithRequests: 0,
      averageBorrowsPerUser: 0
    }
  }
}

// Get overdue analysis
export async function getOverdueAnalysis() {
  try {
    const overdueBooks = await prisma.borrow.findMany({
      where: {
        status: 'OVERDUE'
      },
      include: {
        user: {
          select: { name: true, email: true }
        },
        copy: {
          include: {
            book: {
              select: { title: true, author: true }
            }
          }
        }
      },
      orderBy: {
        dueDate: 'asc'
      }
    })

    const overdueStats = {
      totalOverdue: overdueBooks.length,
      overdueByDays: {
        '1-7': 0,
        '8-14': 0,
        '15-30': 0,
        '30+': 0
      }
    }

    const now = new Date()
    overdueBooks.forEach(borrow => {
      const daysOverdue = Math.floor((now.getTime() - borrow.dueDate.getTime()) / (1000 * 60 * 60 * 24))
      
      if (daysOverdue <= 7) {
        overdueStats.overdueByDays['1-7']++
      } else if (daysOverdue <= 14) {
        overdueStats.overdueByDays['8-14']++
      } else if (daysOverdue <= 30) {
        overdueStats.overdueByDays['15-30']++
      } else {
        overdueStats.overdueByDays['30+']++
      }
    })

    return {
      ...overdueStats,
      overdueBooks: overdueBooks.slice(0, 10) // Return top 10 most overdue
    }
  } catch (error) {
    console.error('Error getting overdue analysis:', error)
    return {
      totalOverdue: 0,
      overdueByDays: {
        '1-7': 0,
        '8-14': 0,
        '15-30': 0,
        '30+': 0
      },
      overdueBooks: []
    }
  }
} 