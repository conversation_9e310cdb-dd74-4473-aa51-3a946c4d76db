"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { createBook, updateBook } from "@/lib/actions/books"
import type { Book, Category } from "@/types"

interface BookFormProps {
  book?: Book
  mode: 'create' | 'edit'
  categories: Category[]
  onSuccess: () => void
  onCancel: () => void
}

export function BookForm({ book, mode, categories, onSuccess, onCancel }: BookFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  const [selectedCategories, setSelectedCategories] = useState<string[]>(
    book?.categories?.map(c => c.id) || []
  )

  async function handleSubmit(formData: FormData) {
    setIsLoading(true)
    setMessage(null)

    try {
      const bookData = {
        title: formData.get('title') as string,
        author: formData.get('author') as string,
        isbn: formData.get('isbn') as string || undefined,
        description: formData.get('description') as string || undefined,
        publisher: formData.get('publisher') as string || undefined,
        publishedYear: formData.get('publishedYear') ? parseInt(formData.get('publishedYear') as string) : undefined,
        pages: formData.get('pages') ? parseInt(formData.get('pages') as string) : undefined,
        language: formData.get('language') as string || 'English',
        coverImageUrl: formData.get('coverImageUrl') as string || undefined,
        hasDigitalCopy: formData.get('hasDigitalCopy') === 'on',
        digitalCopyUrl: formData.get('digitalCopyUrl') as string || undefined,
        categoryIds: selectedCategories,
      }

      let result
      if (mode === 'create') {
        result = await createBook(bookData)
      } else {
        result = await updateBook(book!.id, bookData)
      }

      if (result.success) {
        setMessage({ type: 'success', text: `Book ${mode === 'create' ? 'created' : 'updated'} successfully!` })
        setTimeout(() => onSuccess(), 1500)
      } else {
        setMessage({ type: 'error', text: result.error || `Failed to ${mode} book` })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'An unexpected error occurred' })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form action={handleSubmit} className="space-y-8">
      {message && (
        <div className={`p-4 rounded-md ${
          message.type === 'success' 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {message.text}
        </div>
      )}

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="title" className="text-sm font-medium">
                Title *
              </label>
              <Input
                id="title"
                name="title"
                type="text"
                defaultValue={book?.title || ''}
                required
                disabled={isLoading}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="author" className="text-sm font-medium">
                Author *
              </label>
              <Input
                id="author"
                name="author"
                type="text"
                defaultValue={book?.author || ''}
                required
                disabled={isLoading}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="isbn" className="text-sm font-medium">
                ISBN
              </label>
              <Input
                id="isbn"
                name="isbn"
                type="text"
                defaultValue={book?.isbn || ''}
                disabled={isLoading}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="language" className="text-sm font-medium">
                Language
              </label>
              <Input
                id="language"
                name="language"
                type="text"
                defaultValue={book?.language || 'English'}
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="space-y-2">
            <label htmlFor="description" className="text-sm font-medium">
              Description
            </label>
            <Textarea
              id="description"
              name="description"
              rows={4}
              defaultValue={book?.description || ''}
              disabled={isLoading}
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="coverImageUrl" className="text-sm font-medium">
              Cover Image URL
            </label>
            <Input
              id="coverImageUrl"
              name="coverImageUrl"
              type="url"
              defaultValue={book?.coverImageUrl || ''}
              disabled={isLoading}
            />
          </div>
        </CardContent>
      </Card>

      {/* Publication Details */}
      <Card>
        <CardHeader>
          <CardTitle>Publication Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label htmlFor="publisher" className="text-sm font-medium">
                Publisher
              </label>
              <Input
                id="publisher"
                name="publisher"
                type="text"
                defaultValue={book?.publisher || ''}
                disabled={isLoading}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="publishedYear" className="text-sm font-medium">
                Published Year
              </label>
              <Input
                id="publishedYear"
                name="publishedYear"
                type="number"
                min="1000"
                max={new Date().getFullYear()}
                defaultValue={book?.publishedYear || ''}
                disabled={isLoading}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="pages" className="text-sm font-medium">
                Pages
              </label>
              <Input
                id="pages"
                name="pages"
                type="number"
                min="1"
                defaultValue={book?.pages || ''}
                disabled={isLoading}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Categories */}
      <Card>
        <CardHeader>
          <CardTitle>Categories</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Select Categories
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {categories.map((category) => (
                <label key={category.id} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={selectedCategories.includes(category.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedCategories([...selectedCategories, category.id])
                      } else {
                        setSelectedCategories(selectedCategories.filter(id => id !== category.id))
                      }
                    }}
                    disabled={isLoading}
                  />
                  <span className="text-sm">{category.name}</span>
                </label>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Digital Copy */}
      <Card>
        <CardHeader>
          <CardTitle>Digital Copy</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="hasDigitalCopy"
              name="hasDigitalCopy"
              defaultChecked={book?.hasDigitalCopy || false}
              disabled={isLoading}
            />
            <label htmlFor="hasDigitalCopy" className="text-sm font-medium">
              This book has a digital copy
            </label>
          </div>

          <div className="space-y-2">
            <label htmlFor="digitalCopyUrl" className="text-sm font-medium">
              Digital Copy URL
            </label>
            <Input
              id="digitalCopyUrl"
              name="digitalCopyUrl"
              type="url"
              defaultValue={book?.digitalCopyUrl || ''}
              disabled={isLoading}
            />
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex items-center space-x-4">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? `${mode === 'create' ? 'Creating' : 'Updating'}...` : `${mode === 'create' ? 'Create' : 'Update'} Book`}
        </Button>
        <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
          Cancel
        </Button>
      </div>
    </form>
  )
}
