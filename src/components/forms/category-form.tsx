"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { createCategory, updateCategory } from "@/lib/actions/categories"
import type { Category } from "@/types"

interface CategoryFormProps {
  category?: Category
  mode: 'create' | 'edit'
  onSuccess: () => void
  onCancel: () => void
}

export function CategoryForm({ category, mode, onSuccess, onCancel }: CategoryFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  async function handleSubmit(formData: FormData) {
    setIsLoading(true)
    setMessage(null)

    try {
      const categoryData = {
        name: formData.get('name') as string,
        description: formData.get('description') as string || undefined,
      }

      let result
      if (mode === 'create') {
        result = await createCategory(categoryData)
      } else {
        result = await updateCategory(category!.id, categoryData)
      }

      if (result.success) {
        setMessage({ type: 'success', text: `Category ${mode === 'create' ? 'created' : 'updated'} successfully!` })
        setTimeout(() => onSuccess(), 1500)
      } else {
        setMessage({ type: 'error', text: result.error || `Failed to ${mode} category` })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'An unexpected error occurred' })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form action={handleSubmit} className="space-y-6">
      {message && (
        <div className={`p-4 rounded-md ${
          message.type === 'success' 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {message.text}
        </div>
      )}

      <div className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="name" className="text-sm font-medium">
            Category Name *
          </label>
          <Input
            id="name"
            name="name"
            type="text"
            defaultValue={category?.name || ''}
            required
            disabled={isLoading}
            placeholder="e.g., Fiction, Science, History"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="description" className="text-sm font-medium">
            Description
          </label>
          <Textarea
            id="description"
            name="description"
            rows={3}
            defaultValue={category?.description || ''}
            disabled={isLoading}
            placeholder="Brief description of this category..."
          />
        </div>
      </div>

      <div className="flex items-center space-x-4">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? `${mode === 'create' ? 'Creating' : 'Updating'}...` : `${mode === 'create' ? 'Create' : 'Update'} Category`}
        </Button>
        <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
          Cancel
        </Button>
      </div>
    </form>
  )
}
