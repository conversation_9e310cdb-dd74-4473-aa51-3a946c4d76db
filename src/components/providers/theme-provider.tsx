'use client'

import * as React from "react"
import { useAppStore } from "@/store/app-store"

type Theme = 'light'

interface ThemeProviderProps {
  children: React.ReactNode
  defaultTheme?: Theme
  storageKey?: string
}





export function ThemeProvider({ children }: { children: React.ReactNode }) {
  React.useEffect(() => {
    const root = window.document.documentElement
    root.classList.add('light')
  }, [])

  return <>{children}</>
}




