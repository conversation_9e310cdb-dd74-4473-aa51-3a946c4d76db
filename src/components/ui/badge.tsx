import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary: "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        success: "border-transparent bg-green-100 text-green-800 hover:bg-green-100/80",
        warning: "border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-100/80",
        danger: "border-transparent bg-red-100 text-red-800 hover:bg-red-100/80",
        info: "border-transparent bg-blue-100 text-blue-800 hover:bg-blue-100/80",
        outline: "text-foreground",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, size, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant, size }), className)} {...props} />
  )
}

// Status-specific badge components for common use cases
export function StatusBadge({ status, ...props }: { status: string } & Omit<BadgeProps, 'variant'>) {
  const getVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'available':
        return 'success'
      case 'borrowed':
      case 'reserved':
        return 'warning'
      case 'overdue':
      case 'lost':
        return 'danger'
      case 'maintenance':
        return 'secondary'
      default:
        return 'default'
    }
  }

  return (
    <Badge variant={getVariant(status)} {...props}>
      {status}
    </Badge>
  )
}

export function RoleBadge({ role, ...props }: { role: string } & Omit<BadgeProps, 'variant'>) {
  const getVariant = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'danger'
      case 'librarian':
        return 'warning'
      case 'user':
        return 'info'
      default:
        return 'default'
    }
  }

  return (
    <Badge variant={getVariant(role)} {...props}>
      {role}
    </Badge>
  )
}

export { Badge, badgeVariants }
