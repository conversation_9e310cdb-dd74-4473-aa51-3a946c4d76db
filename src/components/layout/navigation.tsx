'use client'

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useSession } from "next-auth/react"
import { cn } from "@/lib/utils"

interface NavigationProps {
  className?: string
  variant?: 'horizontal' | 'vertical'
  showIcons?: boolean
}

interface NavItem {
  label: string
  href: string
  icon?: React.ReactNode
  roles?: string[]
  external?: boolean
}

const publicNavItems: NavItem[] = [
  {
    label: 'Home',
    href: '/',
    icon: (
      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
      </svg>
    )
  },
  {
    label: 'Books',
    href: '/books',
    icon: (
      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
      </svg>
    )
  },
  {
    label: 'Categories',
    href: '/categories',
    icon: (
      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14-7H5m14 14H5" />
      </svg>
    )
  }
]

const authenticatedNavItems: NavItem[] = [
  {
    label: 'Dashboard',
    href: '/dashboard',
    icon: (
      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v0" />
      </svg>
    ),
    roles: ['USER', 'ADMIN', 'LIBRARIAN']
  },
  {
    label: 'My Books',
    href: '/dashboard/my-books',
    icon: (
      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
    ),
    roles: ['USER']
  },
  {
    label: 'Admin',
    href: '/dashboard/admin',
    icon: (
      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
      </svg>
    ),
    roles: ['ADMIN', 'LIBRARIAN']
  }
]

export function Navigation({ className, variant = 'horizontal', showIcons = true }: NavigationProps) {
  const pathname = usePathname()
  const { data: session } = useSession()

  const userRole = (session?.user as any)?.role || 'USER'

  const hasAccess = (roles?: string[]) => {
    if (!roles) return true
    return roles.includes(userRole)
  }

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }

  const allNavItems = [
    ...publicNavItems,
    ...(session ? authenticatedNavItems.filter(item => hasAccess(item.roles)) : [])
  ]

  const baseClasses = variant === 'horizontal' 
    ? "flex items-center space-x-6" 
    : "flex flex-col space-y-2"

  const linkClasses = variant === 'horizontal'
    ? "flex items-center space-x-2 text-sm font-medium transition-colors hover:text-primary"
    : "flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground"

  return (
    <nav className={cn(baseClasses, className)}>
      {allNavItems.map((item) => {
        const active = isActive(item.href)
        
        const content = (
          <>
            {showIcons && item.icon && item.icon}
            <span>{item.label}</span>
          </>
        )

        if (item.external) {
          return (
            <a
              key={item.href}
              href={item.href}
              target="_blank"
              rel="noopener noreferrer"
              className={cn(
                linkClasses,
                active && variant === 'vertical' && "bg-accent text-accent-foreground",
                active && variant === 'horizontal' && "text-primary"
              )}
            >
              {content}
              <svg className="h-3 w-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
            </a>
          )
        }

        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              linkClasses,
              active && variant === 'vertical' && "bg-accent text-accent-foreground",
              active && variant === 'horizontal' && "text-primary"
            )}
          >
            {content}
          </Link>
        )
      })}
    </nav>
  )
}

// Mobile navigation component
export function MobileNavigation({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 lg:hidden">
      <div className="fixed inset-0 bg-background/80 backdrop-blur-sm" onClick={onClose} />
      <div className="fixed top-0 right-0 h-full w-64 bg-background border-l shadow-lg">
        <div className="flex h-16 items-center justify-between px-4 border-b">
          <span className="font-semibold">Menu</span>
          <button
            onClick={onClose}
            className="rounded-md p-2 hover:bg-accent"
          >
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div className="p-4">
          <Navigation variant="vertical" />
        </div>
      </div>
    </div>
  )
}
