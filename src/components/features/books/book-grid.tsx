"use client"

import * as React from "react"
import { BookCard } from "./book-card"
import { BookCardSkeleton } from "@/components/ui/loading"
import { Pagination } from "@/components/ui/pagination"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import type { BookWithDetails } from "@/types"

interface BookGridProps {
  books: BookWithDetails[]
  totalCount: number
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  loading?: boolean
  viewMode?: 'grid' | 'list'
  onViewModeChange?: (mode: 'grid' | 'list') => void
  className?: string
}

export function BookGrid({
  books,
  totalCount,
  currentPage,
  totalPages,
  onPageChange,
  loading = false,
  viewMode = 'grid',
  onViewModeChange,
  className
}: BookGridProps) {
  if (loading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="flex items-center justify-between">
          <div className="h-6 bg-muted rounded w-32 animate-pulse" />
          <div className="flex space-x-2">
            <div className="h-10 w-10 bg-muted rounded animate-pulse" />
            <div className="h-10 w-10 bg-muted rounded animate-pulse" />
          </div>
        </div>
        <div className={cn(
          "grid gap-6",
          viewMode === 'grid' 
            ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
            : "grid-cols-1"
        )}>
          {Array.from({ length: 12 }).map((_, i) => (
            <BookCardSkeleton key={i} />
          ))}
        </div>
      </div>
    )
  }

  if (!books.length) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="text-center py-12">
          <svg
            className="mx-auto h-16 w-16 text-muted-foreground"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
            />
          </svg>
          <h3 className="mt-4 text-lg font-semibold">No books found</h3>
          <p className="text-muted-foreground">
            Try adjusting your search criteria or browse our categories.
          </p>
          <div className="mt-6">
            <Button onClick={() => window.location.href = '/books'}>
              Browse All Books
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header with Results Count and View Toggle */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {books.length} of {totalCount.toLocaleString()} books
        </div>
        
        {onViewModeChange && (
          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="icon"
              onClick={() => onViewModeChange('grid')}
              aria-label="Grid view"
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="icon"
              onClick={() => onViewModeChange('list')}
              aria-label="List view"
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
            </Button>
          </div>
        )}
      </div>

      {/* Books Grid/List */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {books.map((book) => (
            <BookCard key={book.id} book={book} variant="default" />
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {books.map((book) => (
            <BookListItem key={book.id} book={book} />
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={onPageChange}
          />
        </div>
      )}
    </div>
  )
}

// List view item component
function BookListItem({ book }: { book: BookWithDetails }) {
  const availableCopies = book.copies?.filter(copy => copy.status === 'AVAILABLE').length || 0
  const totalCopies = book.copies?.length || 0
  const isAvailable = availableCopies > 0 || book.hasDigitalCopy

  return (
    <div className="flex space-x-4 p-4 border rounded-lg hover:shadow-md transition-shadow">
      {/* Book Cover */}
      <div className="flex-shrink-0">
        <div className="w-16 h-20 bg-muted rounded overflow-hidden">
          {book.coverImageUrl ? (
            <img
              src={book.coverImageUrl}
              alt={`Cover of ${book.title}`}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <svg className="h-8 w-8 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
          )}
        </div>
      </div>

      {/* Book Info */}
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <h3 className="font-semibold text-lg leading-tight">
              <a href={`/books/${book.id}`} className="hover:text-primary transition-colors">
                {book.title}
              </a>
            </h3>
            <p className="text-muted-foreground">{book.author}</p>
            {book.description && (
              <p className="text-sm text-muted-foreground line-clamp-2">
                {book.description}
              </p>
            )}
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <span>{book.publishedYear}</span>
              <span>{availableCopies}/{totalCopies} available</span>
              {book.hasDigitalCopy && <span>Digital available</span>}
            </div>
          </div>
          
          {/* Actions */}
          <div className="flex flex-col space-y-2">
            <Button size="sm" variant="outline" onClick={() => window.location.href = `/books/${book.id}`}>
              View Details
            </Button>
            {isAvailable && (
              <Button size="sm">Request Borrow</Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
