import * as React from "react"
import Image from "next/image"
import Link from "next/link"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge, StatusBadge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { formatDate, formatFileSize } from "@/lib/utils"
import type { BookWithDetails } from "@/types"

interface BookDetailsProps {
  book: BookWithDetails
  userRole?: string
  userActiveBorrows?: number
  maxBorrows?: number
}

export function BookDetails({ book, userRole, userActiveBorrows = 0, maxBorrows = 5 }: BookDetailsProps) {
  const availableCopies = book.copies?.filter(copy => copy.status === 'AVAILABLE') || []
  const totalCopies = book.copies?.length || 0
  const canBorrow = userActiveBorrows < maxBorrows

  return (
    <div className="space-y-8">
      {/* Main Book Information */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Book Cover */}
        <div className="lg:col-span-1">
          <div className="sticky top-24">
            <div className="aspect-[3/4] relative overflow-hidden rounded-lg bg-muted">
              {book.coverImageUrl ? (
                <Image
                  src={book.coverImageUrl}
                  alt={`Cover of ${book.title}`}
                  fill
                  className="object-cover"
                  priority
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <svg
                    className="h-24 w-24 text-muted-foreground"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                    />
                  </svg>
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <div className="mt-6 space-y-3">
              {availableCopies.length > 0 && canBorrow && (
                <Button className="w-full" size="lg">
                  Request to Borrow
                </Button>
              )}
              
              {book.hasDigitalCopy && (
                <Button variant="outline" className="w-full">
                  <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Download Digital Copy
                </Button>
              )}

              {!canBorrow && (
                <div className="text-sm text-muted-foreground text-center p-3 bg-muted rounded">
                  You have reached the maximum number of borrowed books ({maxBorrows})
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Book Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Title and Author */}
          <div>
            <h1 className="text-3xl font-bold tracking-tight mb-2">{book.title}</h1>
            <p className="text-xl text-muted-foreground mb-4">by {book.author}</p>
            
            {/* Categories */}
            {book.categories && book.categories.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {book.categories.map((category) => (
                  <Link key={category.id} href={`/categories/${category.slug}`}>
                    <Badge variant="outline" className="hover:bg-accent">
                      {category.name}
                    </Badge>
                  </Link>
                ))}
              </div>
            )}
          </div>

          {/* Description */}
          {book.description && (
            <div>
              <h2 className="text-lg font-semibold mb-3">Description</h2>
              <p className="text-muted-foreground leading-relaxed">{book.description}</p>
            </div>
          )}

          {/* Book Details */}
          <Card>
            <CardHeader>
              <CardTitle>Book Details</CardTitle>
            </CardHeader>
            <CardContent>
              <dl className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {book.isbn && (
                  <>
                    <dt className="font-medium">ISBN</dt>
                    <dd className="text-muted-foreground">{book.isbn}</dd>
                  </>
                )}
                {book.publisher && (
                  <>
                    <dt className="font-medium">Publisher</dt>
                    <dd className="text-muted-foreground">{book.publisher}</dd>
                  </>
                )}
                {book.publicationYear && (
                  <>
                    <dt className="font-medium">Published Year</dt>
                    <dd className="text-muted-foreground">{book.publicationYear}</dd>
                  </>
                )}
                {book.pages && (
                  <>
                    <dt className="font-medium">Pages</dt>
                    <dd className="text-muted-foreground">{book.pages}</dd>
                  </>
                )}
                {book.language && (
                  <>
                    <dt className="font-medium">Language</dt>
                    <dd className="text-muted-foreground">{book.language}</dd>
                  </>
                )}
                <dt className="font-medium">Added to Library</dt>
                <dd className="text-muted-foreground">{formatDate(book.createdAt)}</dd>
              </dl>
            </CardContent>
          </Card>

          {/* Digital Copy Information */}
          {book.hasDigitalCopy && (
            <Card>
              <CardHeader>
                <CardTitle>Digital Copy</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">PDF Available</p>
                    <p className="text-sm text-muted-foreground">
                      Download and read offline
                    </p>
                  </div>
                  <Button variant="outline">
                    <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Download
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Physical Copies */}
      {book.copies && book.copies.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Physical Copies ({totalCopies})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {book.copies.map((copy) => (
                <div
                  key={copy.id}
                  className="border rounded-lg p-4 space-y-3"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Copy #{copy.copyCode}</span>
                    <StatusBadge status={copy.status} size="sm" />
                  </div>
                  
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Condition:</span>
                      <span>{copy.condition}</span>
                    </div>
                    {copy.location && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Location:</span>
                        <span>{copy.location}</span>
                      </div>
                    )}
                  </div>

                  {copy.status === 'AVAILABLE' && canBorrow && (
                    <Button size="sm" className="w-full">
                      Request This Copy
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Admin Actions */}
      {(userRole === 'ADMIN' || userRole === 'LIBRARIAN') && (
        <Card>
          <CardHeader>
            <CardTitle>Admin Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Button variant="outline" onClick={() => window.location.href = `/dashboard/admin/books/${book.id}/edit`}>
                Edit Book
              </Button>
              <Button variant="outline">
                Add Copy
              </Button>
              <Button variant="outline">
                View Borrow History
              </Button>
              <Button variant="destructive" size="sm">
                Delete Book
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
