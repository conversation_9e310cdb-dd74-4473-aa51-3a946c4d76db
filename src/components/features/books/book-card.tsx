import * as React from "react"
import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Badge, StatusBadge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { cn, truncateText } from "@/lib/utils"
import type { BookWithDetails } from "@/types"

interface BookCardProps {
  book: BookWithDetails
  className?: string
  showActions?: boolean
  variant?: 'default' | 'compact' | 'featured'
}

export function BookCard({ book, className, showActions = true, variant = 'default' }: BookCardProps) {
  const availableCopies = book.copies?.filter(copy => copy.status === 'AVAILABLE').length || 0
  const totalCopies = book.copies?.length || 0
  
  const isAvailable = availableCopies > 0 || book.hasDigitalCopy

  return (
    <Link href={`/books/${book.id}`} className="block focus:outline-none group">
      <Card className={cn(
        "group overflow-hidden transition-all duration-200 hover:shadow-lg hover:-translate-y-1 h-full flex flex-col p-0 justify-between cursor-pointer",
        variant === 'compact' && "h-full",
        variant === 'featured' && "bg-gradient-to-br from-primary/5 to-secondary/5",
        className
      )}>

      <div className="relative w-full">
        {/* Book Cover */}
        <div className={cn(
          "relative overflow-hidden bg-muted flex-shrink-0 w-full h-[180px]",
          variant === 'compact' ? "" : ""
        )}>
          {book.coverImageUrl ? (
            <Image
              src={book.coverImageUrl}
              alt={`Cover of ${book.title}`}
              fill
              className="object-cover transition-transform duration-200 group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div className="flex items-center justify-center h-full bg-gradient-to-br from-primary/10 to-secondary/10">
              <svg
                className="h-12 w-12 text-muted-foreground"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                />
              </svg>
            </div>
          )}
          
          {/* Status Badge */}
          <div className="absolute top-2 right-2">
            {isAvailable ? (
              <StatusBadge status="available" size="sm" />
            ) : (
              <StatusBadge status="borrowed" size="sm" />
            )}
          </div>

          {/* Digital Copy Badge */}
          {book.hasDigitalCopy && (
            <div className="absolute top-2 left-2">
              <Badge variant="info" size="sm" className="text-xs">
                Digital
              </Badge>
            </div>
          )}
        </div>

        {/* Quick Actions Overlay */}
        {showActions && (
  <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center z-10">
    <div className="flex items-center justify-center w-full h-full">
      <span
        className="font-semibold rounded-lg shadow-sm transition-all duration-150 px-4 py-2 bg-secondary text-secondary-foreground hover:scale-105 hover:bg-secondary/80 focus:ring-2 focus:ring-primary cursor-pointer select-none text-sm"
        tabIndex={-1}
        aria-hidden="true"
      >
        View Details
      </span>
    </div>
  </div>
)}
      </div>

      <CardContent className="p-2 flex-1 flex flex-col justify-between h-full">
        <div>
          {/* Title */}
          <h3 className="font-semibold text-xs leading-tight line-clamp-2 mb-0.5">
            <Link 
              href={`/books/${book.id}`}
              className="hover:text-primary transition-colors"
            >
              {variant === 'compact' ? truncateText(book.title, 40) : book.title}
            </Link>
          </h3>

          {/* Author */}
          <p className="text-xs text-muted-foreground leading-tight line-clamp-1">
            {variant === 'compact' ? truncateText(book.author, 30) : book.author}
          </p>
        </div>

        {/* Availability Info - Pushed to bottom */}
        <div className="mt-1">
          <div className="flex items-center justify-between text-[11px] text-muted-foreground">
            <span className="truncate">
              {availableCopies > 0 ? `${availableCopies} available` : 'Check'}
            </span>
            {book.publicationYear && (
              <span className="flex-shrink-0 ml-1">{book.publicationYear}</span>
            )}
          </div>
        </div>

          {/* Description (for featured variant) */}
          {variant === 'featured' && book.description && (
            <p className="text-sm text-muted-foreground line-clamp-2">
              {truncateText(book.description, 100)}
            </p>
          )}
      </CardContent>
    </Card>
    </Link>
  )
}
