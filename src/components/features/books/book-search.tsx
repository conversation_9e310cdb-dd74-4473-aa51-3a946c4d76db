"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

interface BookSearchProps {
  initialQuery?: string
  onSearch: (query: string) => void
  placeholder?: string
  showAdvanced?: boolean
}

export function BookSearch({ 
  initialQuery = '', 
  onSearch, 
  placeholder = "Search books by title, author, or ISBN...",
  showAdvanced = false 
}: BookSearchProps) {
  const [query, setQuery] = useState(initialQuery)
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false)

  useEffect(() => {
    setQuery(initialQuery)
  }, [initialQuery])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch(query)
  }

  const handleQuickSearch = (searchTerm: string) => {
    setQuery(searchTerm)
    onSearch(searchTerm)
  }

  return (
    <div className="space-y-4">
      {/* Main Search */}
      <form onSubmit={handleSubmit} className="flex space-x-2">
        <div className="flex-1 relative">
          <Input
            type="text"
            placeholder={placeholder}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="pr-10"
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <svg
              className="h-4 w-4 text-muted-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
        </div>
        <Button type="submit">Search</Button>
        {showAdvanced && (
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
          >
            Advanced
          </Button>
        )}
      </form>

      {/* Quick Search Suggestions */}
      <div className="flex flex-wrap gap-2">
        <span className="text-sm text-muted-foreground">Quick search:</span>
        {[
          'Fiction',
          'Science',
          'History',
          'Biography',
          'Technology',
          'Art'
        ].map((term) => (
          <button
            key={term}
            onClick={() => handleQuickSearch(term)}
            className="text-xs px-2 py-1 bg-muted hover:bg-muted/80 rounded-md transition-colors"
          >
            {term}
          </button>
        ))}
      </div>

      {/* Advanced Search */}
      {showAdvancedSearch && (
        <Card>
          <CardContent className="p-4">
            <div className="space-y-4">
              <h3 className="font-medium">Advanced Search</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Title</label>
                  <Input placeholder="Book title..." />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Author</label>
                  <Input placeholder="Author name..." />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">ISBN</label>
                  <Input placeholder="ISBN number..." />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Publisher</label>
                  <Input placeholder="Publisher name..." />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Year Range</label>
                  <div className="flex space-x-2">
                    <Input placeholder="From" type="number" />
                    <Input placeholder="To" type="number" />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Language</label>
                  <select className="w-full px-3 py-2 border rounded-md">
                    <option value="">Any Language</option>
                    <option value="English">English</option>
                    <option value="Spanish">Spanish</option>
                    <option value="French">French</option>
                    <option value="German">German</option>
                  </select>
                </div>
              </div>
              <div className="flex space-x-2">
                <Button>Apply Filters</Button>
                <Button variant="outline">Clear All</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
