"use client"

import { BookCard } from "./book-card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import type { BookWithDetails } from "@/types"

interface BookListProps {
  books: BookWithDetails[]
  showActions?: boolean
  variant?: 'grid' | 'list'
  emptyMessage?: string
  emptyAction?: {
    label: string
    onClick: () => void
  }
}

export function BookList({ 
  books, 
  showActions = false, 
  variant = 'list',
  emptyMessage = "No books found.",
  emptyAction
}: BookListProps) {
  if (!books.length) {
    return (
      <div className="text-center py-12">
        <svg
          className="mx-auto h-12 w-12 text-muted-foreground"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
          />
        </svg>
        <h3 className="mt-4 text-lg font-semibold">No Books Found</h3>
        <p className="text-muted-foreground mb-6">
          {emptyMessage}
        </p>
        {emptyAction && (
          <Button onClick={emptyAction.onClick}>
            {emptyAction.label}
          </Button>
        )}
      </div>
    )
  }

  if (variant === 'list') {
    return (
      <div className="space-y-4">
        {books.map((book) => (
          <Card key={book.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex space-x-4">
                {/* Book Cover */}
                <div className="flex-shrink-0">
                  <div className="w-16 h-20 bg-muted rounded flex items-center justify-center">
                    {book.coverImageUrl ? (
                      <img
                        src={book.coverImageUrl}
                        alt={book.title}
                        className="w-full h-full object-cover rounded"
                      />
                    ) : (
                      <svg
                        className="w-8 h-8 text-muted-foreground"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={1.5}
                          d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                        />
                      </svg>
                    )}
                  </div>
                </div>

                {/* Book Details */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-foreground hover:text-primary cursor-pointer">
                        {book.title}
                      </h3>
                      <p className="text-muted-foreground">by {book.author}</p>
                      
                      {book.description && (
                        <p className="text-sm text-muted-foreground mt-2 line-clamp-2">
                          {book.description}
                        </p>
                      )}

                      <div className="flex items-center space-x-4 mt-3">
                        {book.publishedYear && (
                          <span className="text-xs text-muted-foreground">
                            {book.publishedYear}
                          </span>
                        )}
                        {book.publisher && (
                          <span className="text-xs text-muted-foreground">
                            {book.publisher}
                          </span>
                        )}
                        {book.pages && (
                          <span className="text-xs text-muted-foreground">
                            {book.pages} pages
                          </span>
                        )}
                      </div>

                      {book.categories && book.categories.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {book.categories.slice(0, 3).map((category) => (
                            <span
                              key={category.id}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary"
                            >
                              {category.name}
                            </span>
                          ))}
                          {book.categories.length > 3 && (
                            <span className="text-xs text-muted-foreground">
                              +{book.categories.length - 3} more
                            </span>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex flex-col space-y-2 ml-4">
                      <Button size="sm" onClick={() => window.location.href = `/books/${book.id}`}>
                        View Details
                      </Button>
                      {showActions && (
                        <>
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => window.location.href = `/dashboard/admin/books/${book.id}/edit`}
                          >
                            Edit
                          </Button>
                          <Button size="sm" variant="outline">
                            Manage Copies
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  // Grid variant - fallback to BookGrid component
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
      {books.map((book) => (
        <BookCard key={book.id} book={book} showActions={showActions} />
      ))}
    </div>
  )
}
