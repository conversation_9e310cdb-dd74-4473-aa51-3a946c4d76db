'use client'

import * as React from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Select } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { cn, debounce } from "@/lib/utils"
import type { Category } from "@/types"

interface BookFiltersProps {
  categories: Category[]
  className?: string
}

interface FilterState {
  query: string
  categories: string[]
  availability: string
  sortBy: string
  language: string
  yearFrom: string
  yearTo: string
}

const sortOptions = [
  { value: 'relevance', label: 'Relevance' },
  { value: 'title', label: 'Title A-Z' },
  { value: 'title_desc', label: 'Title Z-A' },
  { value: 'author', label: 'Author A-Z' },
  { value: 'year', label: 'Year (Newest)' },
  { value: 'year_desc', label: 'Year (Oldest)' },
  { value: 'popular', label: 'Most Popular' },
]

const availabilityOptions = [
  { value: 'all', label: 'All Books' },
  { value: 'available', label: 'Available Now' },
  { value: 'digital', label: 'Digital Only' },
  { value: 'physical', label: 'Physical Only' },
]

const languageOptions = [
  { value: 'all', label: 'All Languages' },
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Spanish' },
  { value: 'fr', label: 'French' },
  { value: 'de', label: 'German' },
  { value: 'it', label: 'Italian' },
  { value: 'pt', label: 'Portuguese' },
]

export function BookFilters({ categories, className }: BookFiltersProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [filters, setFilters] = React.useState<FilterState>({
    query: searchParams.get('q') || '',
    categories: searchParams.get('categories')?.split(',').filter(Boolean) || [],
    availability: searchParams.get('availability') || 'all',
    sortBy: searchParams.get('sortBy') || 'relevance',
    language: searchParams.get('language') || 'all',
    yearFrom: searchParams.get('yearFrom') || '',
    yearTo: searchParams.get('yearTo') || '',
  })

  const [showAdvanced, setShowAdvanced] = React.useState(false)

  // Debounced search function
  const debouncedUpdateURL = React.useMemo(
    () => debounce((newFilters: FilterState) => {
      const params = new URLSearchParams()
      
      if (newFilters.query) params.set('q', newFilters.query)
      if (newFilters.categories.length) params.set('categories', newFilters.categories.join(','))
      if (newFilters.availability !== 'all') params.set('availability', newFilters.availability)
      if (newFilters.sortBy !== 'relevance') params.set('sortBy', newFilters.sortBy)
      if (newFilters.language !== 'all') params.set('language', newFilters.language)
      if (newFilters.yearFrom) params.set('yearFrom', newFilters.yearFrom)
      if (newFilters.yearTo) params.set('yearTo', newFilters.yearTo)

      const queryString = params.toString()
      router.push(`/books${queryString ? `?${queryString}` : ''}`)
    }, 500),
    [router]
  )

  // Update URL when filters change
  React.useEffect(() => {
    debouncedUpdateURL(filters)
  }, [filters, debouncedUpdateURL])

  const updateFilter = (key: keyof FilterState, value: string | string[]) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const toggleCategory = (categoryId: string) => {
    setFilters(prev => ({
      ...prev,
      categories: prev.categories.includes(categoryId)
        ? prev.categories.filter(id => id !== categoryId)
        : [...prev.categories, categoryId]
    }))
  }

  const clearFilters = () => {
    setFilters({
      query: '',
      categories: [],
      availability: 'all',
      sortBy: 'relevance',
      language: 'all',
      yearFrom: '',
      yearTo: '',
    })
  }

  const hasActiveFilters = filters.categories.length > 0 || 
    filters.availability !== 'all' || 
    filters.language !== 'all' || 
    filters.yearFrom || 
    filters.yearTo

  return (
    <div className={cn("space-y-6", className)}>
      {/* Search Bar */}
      <div className="space-y-4">
        <Input
          type="search"
          placeholder="Search books, authors, ISBN..."
          value={filters.query}
          onChange={(e) => updateFilter('query', e.target.value)}
          leftIcon={
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          }
        />

        {/* Quick Filters */}
        <div className="flex flex-wrap gap-2">
          <Select
            options={sortOptions}
            value={filters.sortBy}
            onChange={(value) => updateFilter('sortBy', value)}
            placeholder="Sort by"
          />
          <Select
            options={availabilityOptions}
            value={filters.availability}
            onChange={(value) => updateFilter('availability', value)}
            placeholder="Availability"
          />
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
          >
            Advanced Filters
            <svg className={cn("h-4 w-4 ml-2 transition-transform", showAdvanced && "rotate-180")} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </Button>
          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              Clear Filters
            </Button>
          )}
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Advanced Filters</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Categories */}
            <div className="space-y-3">
              <label className="text-sm font-medium">Categories</label>
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <Badge
                    key={category.id}
                    variant={filters.categories.includes(category.id) ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => toggleCategory(category.id)}
                  >
                    {category.name}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Language */}
            <div className="space-y-3">
              <label className="text-sm font-medium">Language</label>
              <Select
                options={languageOptions}
                value={filters.language}
                onChange={(value) => updateFilter('language', value)}
                placeholder="Select language"
              />
            </div>

            {/* Publication Year Range */}
            <div className="space-y-3">
              <label className="text-sm font-medium">Publication Year</label>
              <div className="grid grid-cols-2 gap-4">
                <Input
                  type="number"
                  placeholder="From year"
                  value={filters.yearFrom}
                  onChange={(e) => updateFilter('yearFrom', e.target.value)}
                  min="1000"
                  max={new Date().getFullYear()}
                />
                <Input
                  type="number"
                  placeholder="To year"
                  value={filters.yearTo}
                  onChange={(e) => updateFilter('yearTo', e.target.value)}
                  min="1000"
                  max={new Date().getFullYear()}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Active Filters */}
      {hasActiveFilters && (
        <div className="space-y-2">
          <div className="text-sm font-medium">Active Filters:</div>
          <div className="flex flex-wrap gap-2">
            {filters.categories.map((categoryId) => {
              const category = categories.find(c => c.id === categoryId)
              return category ? (
                <Badge key={categoryId} variant="secondary" className="gap-1">
                  {category.name}
                  <button
                    onClick={() => toggleCategory(categoryId)}
                    className="ml-1 hover:bg-destructive hover:text-destructive-foreground rounded-full"
                  >
                    <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </Badge>
              ) : null
            })}
            {filters.availability !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                {availabilityOptions.find(o => o.value === filters.availability)?.label}
                <button onClick={() => updateFilter('availability', 'all')}>
                  <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </Badge>
            )}
            {filters.language !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                {languageOptions.find(o => o.value === filters.language)?.label}
                <button onClick={() => updateFilter('language', 'all')}>
                  <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </Badge>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
