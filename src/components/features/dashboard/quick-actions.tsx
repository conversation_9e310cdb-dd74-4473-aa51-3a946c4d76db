import * as React from "react"
import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface QuickAction {
  title: string
  description: string
  href: string
  icon: React.ReactNode
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
  external?: boolean
}

interface QuickActionsProps {
  userRole?: string
  className?: string
}

const userActions: QuickAction[] = [
  {
    title: "Browse Books",
    description: "Discover new books in our collection",
    href: "/books",
    icon: (
      <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
      </svg>
    ),
    color: 'primary'
  },
  {
    title: "My Books",
    description: "View your borrowed books and history",
    href: "/dashboard/my-books",
    icon: (
      <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
    ),
    color: 'secondary'
  },
  {
    title: "Search Books",
    description: "Find specific books or authors",
    href: "/books/search",
    icon: (
      <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
    ),
    color: 'success'
  },
  {
    title: "Categories",
    description: "Browse books by category",
    href: "/categories",
    icon: (
      <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14-7H5m14 14H5" />
      </svg>
    ),
    color: 'warning'
  }
]

const adminActions: QuickAction[] = [
  {
    title: "Add New Book",
    description: "Add a new book to the library",
    href: "/dashboard/admin/books/new",
    icon: (
      <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
      </svg>
    ),
    color: 'primary'
  },
  {
    title: "Manage Books",
    description: "Edit and organize book collection",
    href: "/dashboard/admin/books",
    icon: (
      <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
      </svg>
    ),
    color: 'secondary'
  },
  {
    title: "Pending Requests",
    description: "Review borrow requests",
    href: "/dashboard/admin/borrows",
    icon: (
      <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
      </svg>
    ),
    color: 'warning'
  },
  {
    title: "User Management",
    description: "Manage user accounts and roles",
    href: "/dashboard/admin/users",
    icon: (
      <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>
    ),
    color: 'danger'
  }
]

const colorVariants = {
  primary: 'bg-primary/10 text-primary hover:bg-primary/20',
  secondary: 'bg-secondary/10 text-secondary-foreground hover:bg-secondary/20',
  success: 'bg-green-100 text-green-700 hover:bg-green-200',
  warning: 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200',
  danger: 'bg-red-100 text-red-700 hover:bg-red-200'
}

function QuickActionCard({ action }: { action: QuickAction }) {
  const colorClass = colorVariants[action.color || 'primary']

  const content = (
    <div className={cn(
      "flex items-center space-x-4 p-4 rounded-lg transition-colors cursor-pointer",
      colorClass
    )}>
      <div className="flex-shrink-0">
        {action.icon}
      </div>
      <div className="flex-1 min-w-0">
        <h3 className="font-semibold text-sm">{action.title}</h3>
        <p className="text-xs opacity-80 mt-1">{action.description}</p>
      </div>
      <div className="flex-shrink-0">
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </div>
    </div>
  )

  if (action.external) {
    return (
      <a href={action.href} target="_blank" rel="noopener noreferrer">
        {content}
      </a>
    )
  }

  return (
    <Link href={action.href}>
      {content}
    </Link>
  )
}

export function QuickActions({ userRole = 'USER', className }: QuickActionsProps) {
  const isAdmin = userRole === 'ADMIN' || userRole === 'LIBRARIAN'
  const actions = isAdmin ? [...userActions, ...adminActions] : userActions

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {actions.map((action) => (
            <QuickActionCard key={action.href} action={action} />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
