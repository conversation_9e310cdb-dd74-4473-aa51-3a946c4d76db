import * as React from "react"
import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { formatRelativeDate } from "@/lib/utils"
import { cn } from "@/lib/utils"

interface ActivityItem {
  id: string
  type: 'BORROW_REQUEST' | 'BORROW_APPROVED' | 'BORROW_REJECTED' | 'BOOK_RETURNED' | 'BOOK_RENEWED' | 'NOTIFICATION'
  title: string
  description: string
  timestamp: Date | string
  bookId?: string
  bookTitle?: string
  status?: string
}

interface RecentActivityProps {
  activities: ActivityItem[]
  maxItems?: number
  className?: string
  loading?: boolean
}

const activityIcons = {
  BORROW_REQUEST: (
    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
    </svg>
  ),
  BORROW_APPROVED: (
    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  BORROW_REJECTED: (
    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  BOOK_RETURNED: (
    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
    </svg>
  ),
  BOOK_RENEWED: (
    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
    </svg>
  ),
  NOTIFICATION: (
    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25v-2.25L4.5 12V9.75a6 6 0 0 1 6-6z" />
    </svg>
  )
}

const activityColors = {
  BORROW_REQUEST: 'text-blue-600 bg-blue-100',
  BORROW_APPROVED: 'text-green-600 bg-green-100',
  BORROW_REJECTED: 'text-red-600 bg-red-100',
  BOOK_RETURNED: 'text-purple-600 bg-purple-100',
  BOOK_RENEWED: 'text-orange-600 bg-orange-100',
  NOTIFICATION: 'text-gray-600 bg-gray-100'
}

function ActivityIcon({ type }: { type: ActivityItem['type'] }) {
  return (
    <div className={cn(
      "flex h-8 w-8 items-center justify-center rounded-full",
      activityColors[type]
    )}>
      {activityIcons[type]}
    </div>
  )
}

function ActivityItemComponent({ activity }: { activity: ActivityItem }) {
  return (
    <div className="flex space-x-3">
      <ActivityIcon type={activity.type} />
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <p className="text-sm font-medium leading-tight">
              {activity.bookId && activity.bookTitle ? (
                <Link 
                  href={`/books/${activity.bookId}`}
                  className="hover:text-primary transition-colors"
                >
                  {activity.title}
                </Link>
              ) : (
                activity.title
              )}
            </p>
            <p className="text-sm text-muted-foreground">
              {activity.description}
            </p>
            {activity.status && (
              <Badge variant="outline" size="sm">
                {activity.status}
              </Badge>
            )}
          </div>
          <time className="text-xs text-muted-foreground whitespace-nowrap">
            {formatRelativeDate(activity.timestamp)}
          </time>
        </div>
      </div>
    </div>
  )
}

export function RecentActivity({ 
  activities, 
  maxItems = 10, 
  className,
  loading = false 
}: RecentActivityProps) {
  const displayActivities = activities.slice(0, maxItems)

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex space-x-3 animate-pulse">
                <div className="h-8 w-8 bg-muted rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4" />
                  <div className="h-3 bg-muted rounded w-1/2" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!activities.length) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <svg
              className="mx-auto h-12 w-12 text-muted-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
            <h3 className="mt-4 text-sm font-semibold">No recent activity</h3>
            <p className="text-sm text-muted-foreground">
              Your library activity will appear here.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {displayActivities.map((activity) => (
            <ActivityItemComponent key={activity.id} activity={activity} />
          ))}
        </div>
        {activities.length > maxItems && (
          <div className="mt-4 text-center">
            <Link
              href="/dashboard/activity"
              className="text-sm text-primary hover:underline"
            >
              View all activity →
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
