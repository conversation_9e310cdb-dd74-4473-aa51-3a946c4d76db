import * as React from "react"
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface StatsCardProps {
  title: string
  value: number | string
  trend?: {
    direction: 'up' | 'down' | 'neutral'
    percentage: number
    label?: string
  }
  icon?: React.ReactNode
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  className?: string
  loading?: boolean
}

const colorVariants = {
  primary: {
    bg: "bg-primary/10",
    text: "text-primary",
    icon: "text-primary"
  },
  success: {
    bg: "bg-green-100",
    text: "text-green-800",
    icon: "text-green-600"
  },
  warning: {
    bg: "bg-yellow-100",
    text: "text-yellow-800",
    icon: "text-yellow-600"
  },
  danger: {
    bg: "bg-red-100",
    text: "text-red-800",
    icon: "text-red-600"
  },
  info: {
    bg: "bg-blue-100",
    text: "text-blue-800",
    icon: "text-blue-600"
  }
}

export function StatsCard({ 
  title, 
  value, 
  trend, 
  icon, 
  color = 'primary', 
  className,
  loading = false 
}: StatsCardProps) {
  const colors = colorVariants[color]

  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2 flex-1">
              <div className="h-4 bg-muted rounded w-24 animate-pulse" />
              <div className="h-8 bg-muted rounded w-16 animate-pulse" />
              <div className="h-3 bg-muted rounded w-20 animate-pulse" />
            </div>
            <div className="h-12 w-12 bg-muted rounded-lg animate-pulse" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("transition-all duration-200 hover:shadow-md", className)}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <p className="text-sm font-medium text-muted-foreground">
              {title}
            </p>
            <p className="text-3xl font-bold">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </p>
            {trend && (
              <div className="flex items-center space-x-1 text-sm">
                {trend.direction === 'up' && (
                  <svg className="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
                  </svg>
                )}
                {trend.direction === 'down' && (
                  <svg className="h-4 w-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10h10" />
                  </svg>
                )}
                {trend.direction === 'neutral' && (
                  <svg className="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                  </svg>
                )}
                <span className={cn(
                  "font-medium",
                  trend.direction === 'up' && "text-green-600",
                  trend.direction === 'down' && "text-red-600",
                  trend.direction === 'neutral' && "text-muted-foreground"
                )}>
                  {trend.percentage}%
                </span>
                {trend.label && (
                  <span className="text-muted-foreground">
                    {trend.label}
                  </span>
                )}
              </div>
            )}
          </div>
          {icon && (
            <div className={cn(
              "h-12 w-12 rounded-lg flex items-center justify-center",
              colors.bg
            )}>
              <div className={colors.icon}>
                {icon}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Pre-built stats cards for common use cases
export function TotalBooksCard({ count, loading }: { count: number; loading?: boolean }) {
  return (
    <StatsCard
      title="Total Books"
      value={count}
      color="primary"
      loading={loading}
      icon={
        <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      }
    />
  )
}

export function ActiveUsersCard({ count, loading }: { count: number; loading?: boolean }) {
  return (
    <StatsCard
      title="Active Users"
      value={count}
      color="success"
      loading={loading}
      icon={
        <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      }
    />
  )
}

export function BorrowedBooksCard({ count, loading }: { count: number; loading?: boolean }) {
  return (
    <StatsCard
      title="Books Borrowed"
      value={count}
      color="warning"
      loading={loading}
      icon={
        <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      }
    />
  )
}

export function OverdueBooksCard({ count, loading }: { count: number; loading?: boolean }) {
  return (
    <StatsCard
      title="Overdue Books"
      value={count}
      color="danger"
      loading={loading}
      icon={
        <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      }
    />
  )
}
