import * as React from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { formatDate, formatRelativeDate } from "@/lib/utils"
import { cn } from "@/lib/utils"
import type { BorrowRequestWithDetails } from "@/types"

interface PendingRequestsProps {
  requests: BorrowRequestWithDetails[]
  onApprove?: (requestId: string) => void
  onReject?: (requestId: string) => void
  loading?: boolean
  maxItems?: number
}

function RequestItem({ 
  request, 
  onApprove, 
  onReject 
}: { 
  request: BorrowRequestWithDetails
  onApprove?: (requestId: string) => void
  onReject?: (requestId: string) => void
}) {
  const [isProcessing, setIsProcessing] = React.useState(false)

  const handleApprove = async () => {
    if (!onApprove) return
    setIsProcessing(true)
    try {
      await onApprove(request.id)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleReject = async () => {
    if (!onReject) return
    setIsProcessing(true)
    try {
      await onReject(request.id)
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="flex items-center space-x-4 p-4 border rounded-lg">
      {/* Book Cover */}
      <div className="flex-shrink-0">
        <div className="w-12 h-16 bg-muted rounded overflow-hidden">
          {request.bookCopy.book.coverImageUrl ? (
            <Image
              src={request.bookCopy.book.coverImageUrl}
              alt={`Cover of ${request.bookCopy.book.title}`}
              width={48}
              height={64}
              className="object-cover w-full h-full"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <svg className="h-6 w-6 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
          )}
        </div>
      </div>

      {/* Request Details */}
      <div className="flex-1 min-w-0">
        <div className="space-y-1">
          <h4 className="font-medium text-sm leading-tight">
            <Link 
              href={`/books/${request.bookCopy.book.id}`}
              className="hover:text-primary transition-colors"
            >
              {request.bookCopy.book.title}
            </Link>
          </h4>
          <p className="text-sm text-muted-foreground">
            by {request.bookCopy.book.author}
          </p>
          <div className="flex items-center space-x-4 text-xs text-muted-foreground">
            <span>Copy: {request.bookCopy.copyCode}</span>
            <span>Requested: {formatRelativeDate(request.requestDate)}</span>
          </div>
        </div>
      </div>

      {/* User Info */}
      <div className="flex-shrink-0 text-right">
        <div className="space-y-1">
          <p className="text-sm font-medium">
            <Link 
              href={`/dashboard/admin/users/${request.user.id}`}
              className="hover:text-primary transition-colors"
            >
              {request.user.name}
            </Link>
          </p>
          <p className="text-xs text-muted-foreground">{request.user.email}</p>
          <Badge variant="outline" size="sm">
            {request.user.role}
          </Badge>
        </div>
      </div>

      {/* Actions */}
      <div className="flex-shrink-0">
        <div className="flex space-x-2">
          <Button
            size="sm"
            onClick={handleApprove}
            disabled={isProcessing}
            loading={isProcessing}
          >
            Approve
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={handleReject}
            disabled={isProcessing}
          >
            Reject
          </Button>
        </div>
      </div>
    </div>
  )
}

export function PendingRequests({ 
  requests, 
  onApprove, 
  onReject, 
  loading = false,
  maxItems = 5 
}: PendingRequestsProps) {
  const displayRequests = requests.slice(0, maxItems)

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Pending Requests</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg animate-pulse">
                <div className="w-12 h-16 bg-muted rounded" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4" />
                  <div className="h-3 bg-muted rounded w-1/2" />
                  <div className="h-3 bg-muted rounded w-1/4" />
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-muted rounded w-20" />
                  <div className="h-3 bg-muted rounded w-16" />
                </div>
                <div className="flex space-x-2">
                  <div className="h-8 w-16 bg-muted rounded" />
                  <div className="h-8 w-16 bg-muted rounded" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!requests.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Pending Requests</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <svg
              className="mx-auto h-12 w-12 text-muted-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <h3 className="mt-4 text-lg font-semibold">All caught up!</h3>
            <p className="text-muted-foreground">
              No pending borrow requests at the moment.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Pending Requests ({requests.length})</CardTitle>
        {requests.length > maxItems && (
          <Button variant="outline" size="sm" onClick={() => window.location.href = "/dashboard/admin/borrows?filter=pending"}>
            View All
          </Button>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {displayRequests.map((request) => (
            <RequestItem
              key={request.id}
              request={request}
              onApprove={onApprove}
              onReject={onReject}
            />
          ))}
        </div>
        {requests.length > maxItems && (
          <div className="mt-4 text-center">
            <Link
              href="/dashboard/admin/borrows?filter=pending"
              className="text-sm text-primary hover:underline"
            >
              View {requests.length - maxItems} more requests →
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
