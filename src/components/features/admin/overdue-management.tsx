import * as React from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { formatDate, getDaysUntilDue, isOverdue } from "@/lib/utils"
import { cn } from "@/lib/utils"
import type { BorrowWithDetails } from "@/types"

interface OverdueManagementProps {
  overdueBooks: BorrowWithDetails[]
  onSendReminder?: (borrowId: string) => void
  onMarkReturned?: (borrowId: string) => void
  loading?: boolean
  maxItems?: number
}

function OverdueItem({ 
  borrow, 
  onSendReminder, 
  onMarkReturned 
}: { 
  borrow: BorrowWithDetails
  onSendReminder?: (borrowId: string) => void
  onMarkReturned?: (borrowId: string) => void
}) {
  const [isProcessing, setIsProcessing] = React.useState(false)
  const daysOverdue = Math.abs(getDaysUntilDue(borrow.dueDate))

  const handleSendReminder = async () => {
    if (!onSendReminder) return
    setIsProcessing(true)
    try {
      await onSendReminder(borrow.id)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleMarkReturned = async () => {
    if (!onMarkReturned) return
    setIsProcessing(true)
    try {
      await onMarkReturned(borrow.id)
    } finally {
      setIsProcessing(false)
    }
  }

  const getSeverityColor = () => {
    if (daysOverdue >= 30) return 'danger'
    if (daysOverdue >= 14) return 'warning'
    return 'info'
  }

  return (
    <div className={cn(
      "flex items-center space-x-4 p-4 border rounded-lg",
      daysOverdue >= 30 && "border-destructive bg-destructive/5",
      daysOverdue >= 14 && daysOverdue < 30 && "border-yellow-500 bg-yellow-50",
      daysOverdue < 14 && "border-blue-500 bg-blue-50"
    )}>
      {/* Book Cover */}
      <div className="flex-shrink-0">
        <div className="w-12 h-16 bg-muted rounded overflow-hidden">
          {borrow.bookCopy.book.coverImageUrl ? (
            <Image
              src={borrow.bookCopy.book.coverImageUrl}
              alt={`Cover of ${borrow.bookCopy.book.title}`}
              width={48}
              height={64}
              className="object-cover w-full h-full"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <svg className="h-6 w-6 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
          )}
        </div>
      </div>

      {/* Book Details */}
      <div className="flex-1 min-w-0">
        <div className="space-y-1">
          <h4 className="font-medium text-sm leading-tight">
            <Link 
              href={`/books/${borrow.bookCopy.book.id}`}
              className="hover:text-primary transition-colors"
            >
              {borrow.bookCopy.book.title}
            </Link>
          </h4>
          <p className="text-sm text-muted-foreground">
            by {borrow.bookCopy.book.author}
          </p>
          <div className="flex items-center space-x-4 text-xs text-muted-foreground">
            <span>Copy: {borrow.bookCopy.copyCode}</span>
            <span>Due: {formatDate(borrow.dueDate)}</span>
            <Badge variant={getSeverityColor()} size="sm">
              {daysOverdue} days overdue
            </Badge>
          </div>
        </div>
      </div>

      {/* User Info */}
      <div className="flex-shrink-0 text-right">
        <div className="space-y-1">
          <p className="text-sm font-medium">
            <Link 
              href={`/dashboard/admin/users/${borrow.user.id}`}
              className="hover:text-primary transition-colors"
            >
              {borrow.user.name}
            </Link>
          </p>
          <p className="text-xs text-muted-foreground">{borrow.user.email}</p>
          <div className="flex items-center space-x-2 text-xs">
            <span className="text-muted-foreground">Contact:</span>
            {borrow.user.phone && (
              <a 
                href={`tel:${borrow.user.phone}`}
                className="text-primary hover:underline"
              >
                Call
              </a>
            )}
            <a 
              href={`mailto:${borrow.user.email}`}
              className="text-primary hover:underline"
            >
              Email
            </a>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex-shrink-0">
        <div className="flex flex-col space-y-2">
          <Button
            size="sm"
            variant="outline"
            onClick={handleSendReminder}
            disabled={isProcessing}
            loading={isProcessing}
          >
            Send Reminder
          </Button>
          <Button
            size="sm"
            onClick={handleMarkReturned}
            disabled={isProcessing}
          >
            Mark Returned
          </Button>
        </div>
      </div>
    </div>
  )
}

export function OverdueManagement({ 
  overdueBooks, 
  onSendReminder, 
  onMarkReturned, 
  loading = false,
  maxItems = 5 
}: OverdueManagementProps) {
  const displayBooks = overdueBooks.slice(0, maxItems)

  // Group by severity
  const criticalOverdue = overdueBooks.filter(b => Math.abs(getDaysUntilDue(b.dueDate)) >= 30)
  const moderateOverdue = overdueBooks.filter(b => {
    const days = Math.abs(getDaysUntilDue(b.dueDate))
    return days >= 14 && days < 30
  })
  const recentOverdue = overdueBooks.filter(b => Math.abs(getDaysUntilDue(b.dueDate)) < 14)

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Overdue Books</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg animate-pulse">
                <div className="w-12 h-16 bg-muted rounded" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4" />
                  <div className="h-3 bg-muted rounded w-1/2" />
                  <div className="h-3 bg-muted rounded w-1/4" />
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-muted rounded w-20" />
                  <div className="h-3 bg-muted rounded w-16" />
                </div>
                <div className="flex flex-col space-y-2">
                  <div className="h-8 w-20 bg-muted rounded" />
                  <div className="h-8 w-20 bg-muted rounded" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!overdueBooks.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Overdue Books</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <svg
              className="mx-auto h-12 w-12 text-green-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <h3 className="mt-4 text-lg font-semibold text-green-800">No overdue books!</h3>
            <p className="text-muted-foreground">
              All books are returned on time or within the grace period.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Overdue Books ({overdueBooks.length})</CardTitle>
          <div className="flex items-center space-x-4 mt-2 text-sm">
            {criticalOverdue.length > 0 && (
              <Badge variant="danger" size="sm">
                {criticalOverdue.length} Critical (30+ days)
              </Badge>
            )}
            {moderateOverdue.length > 0 && (
              <Badge variant="warning" size="sm">
                {moderateOverdue.length} Moderate (14-29 days)
              </Badge>
            )}
            {recentOverdue.length > 0 && (
              <Badge variant="info" size="sm">
                {recentOverdue.length} Recent (1-13 days)
              </Badge>
            )}
          </div>
        </div>
        {overdueBooks.length > maxItems && (
          <Button variant="outline" size="sm" onClick={() => window.location.href = "/dashboard/admin/borrows?filter=overdue"}>
            View All
          </Button>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {displayBooks.map((borrow) => (
            <OverdueItem
              key={borrow.id}
              borrow={borrow}
              onSendReminder={onSendReminder}
              onMarkReturned={onMarkReturned}
            />
          ))}
        </div>
        {overdueBooks.length > maxItems && (
          <div className="mt-4 text-center">
            <Link
              href="/dashboard/admin/borrows?filter=overdue"
              className="text-sm text-primary hover:underline"
            >
              View {overdueBooks.length - maxItems} more overdue books →
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
