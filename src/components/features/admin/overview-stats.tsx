import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface OverviewStatsProps {
  stats: {
    totalBooks: number
    totalUsers: number
    activeBorrows: number
    overdueBooks: number
    pendingRequests: number
    totalCategories: number
    booksAddedThisMonth: number
    newUsersThisMonth: number
  }
  trends?: {
    booksGrowth: number
    usersGrowth: number
    borrowsGrowth: number
  }
  loading?: boolean
}

interface StatCardProps {
  title: string
  value: number | string
  change?: number
  changeLabel?: string
  icon: React.ReactNode
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  href?: string
}

const colorVariants = {
  primary: {
    bg: "bg-primary/10",
    text: "text-primary",
    icon: "text-primary"
  },
  success: {
    bg: "bg-green-100",
    text: "text-green-800",
    icon: "text-green-600"
  },
  warning: {
    bg: "bg-yellow-100",
    text: "text-yellow-800",
    icon: "text-yellow-600"
  },
  danger: {
    bg: "bg-red-100",
    text: "text-red-800",
    icon: "text-red-600"
  },
  info: {
    bg: "bg-blue-100",
    text: "text-blue-800",
    icon: "text-blue-600"
  }
}

function StatCard({ title, value, change, changeLabel, icon, color = 'primary', href }: StatCardProps) {
  const colors = colorVariants[color]

  const content = (
    <Card className="transition-all duration-200 hover:shadow-md">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <p className="text-sm font-medium text-muted-foreground">
              {title}
            </p>
            <p className="text-3xl font-bold">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </p>
            {change !== undefined && (
              <div className="flex items-center space-x-1 text-sm">
                {change > 0 && (
                  <svg className="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
                  </svg>
                )}
                {change < 0 && (
                  <svg className="h-4 w-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10h10" />
                  </svg>
                )}
                <span className={cn(
                  "font-medium",
                  change > 0 && "text-green-600",
                  change < 0 && "text-red-600",
                  change === 0 && "text-muted-foreground"
                )}>
                  {change > 0 ? '+' : ''}{change}%
                </span>
                {changeLabel && (
                  <span className="text-muted-foreground">
                    {changeLabel}
                  </span>
                )}
              </div>
            )}
          </div>
          <div className={cn(
            "h-12 w-12 rounded-lg flex items-center justify-center",
            colors.bg
          )}>
            <div className={colors.icon}>
              {icon}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  if (href) {
    return (
      <a href={href} className="block">
        {content}
      </a>
    )
  }

  return content
}

export function OverviewStats({ stats, trends, loading = false }: OverviewStatsProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2 flex-1">
                  <div className="h-4 bg-muted rounded w-24 animate-pulse" />
                  <div className="h-8 bg-muted rounded w-16 animate-pulse" />
                  <div className="h-3 bg-muted rounded w-20 animate-pulse" />
                </div>
                <div className="h-12 w-12 bg-muted rounded-lg animate-pulse" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Primary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Books"
          value={stats.totalBooks}
          change={trends?.booksGrowth}
          changeLabel="this month"
          href="/dashboard/admin/books"
          icon={
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          }
          color="primary"
        />

        <StatCard
          title="Total Users"
          value={stats.totalUsers}
          change={trends?.usersGrowth}
          changeLabel="this month"
          href="/dashboard/admin/users"
          icon={
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          }
          color="success"
        />

        <StatCard
          title="Active Borrows"
          value={stats.activeBorrows}
          change={trends?.borrowsGrowth}
          changeLabel="this month"
          href="/dashboard/admin/borrows"
          icon={
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          }
          color="info"
        />

        <StatCard
          title="Overdue Books"
          value={stats.overdueBooks}
          href="/dashboard/admin/borrows?filter=overdue"
          icon={
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          }
          color={stats.overdueBooks > 0 ? "danger" : "success"}
        />
      </div>

      {/* Secondary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Pending Requests"
          value={stats.pendingRequests}
          href="/dashboard/admin/borrows?filter=pending"
          icon={
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
          color={stats.pendingRequests > 0 ? "warning" : "success"}
        />

        <StatCard
          title="Categories"
          value={stats.totalCategories}
          href="/dashboard/admin/categories"
          icon={
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14-7H5m14 14H5" />
            </svg>
          }
          color="info"
        />

        <StatCard
          title="Books Added"
          value={stats.booksAddedThisMonth}
          changeLabel="this month"
          icon={
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          }
          color="primary"
        />

        <StatCard
          title="New Users"
          value={stats.newUsersThisMonth}
          changeLabel="this month"
          icon={
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
            </svg>
          }
          color="success"
        />
      </div>
    </div>
  )
}
