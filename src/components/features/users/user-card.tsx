"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import type { User } from "@/types"

interface UserCardProps {
  user: User
  showActions?: boolean
  variant?: 'default' | 'compact'
}

export function UserCard({ user, showActions = false, variant = 'default' }: UserCardProps) {
  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'destructive'
      case 'LIBRARIAN':
        return 'default'
      case 'USER':
      default:
        return 'secondary'
    }
  }

  if (variant === 'compact') {
    return (
      <div className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors">
        <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
          <span className="text-sm font-semibold text-primary">
            {user.name.charAt(0).toUpperCase()}
          </span>
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <p className="font-medium truncate">{user.name}</p>
            <Badge variant={getRoleBadgeVariant(user.role)} className="text-xs">
              {user.role}
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground truncate">{user.email}</p>
        </div>
        {showActions && (
          <Button size="sm" variant="outline">
            View
          </Button>
        )}
      </div>
    )
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-start space-x-4">
          {/* User Avatar */}
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
            <span className="text-xl font-semibold text-primary">
              {user.name.charAt(0).toUpperCase()}
            </span>
          </div>

          {/* User Details */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="text-lg font-semibold truncate">{user.name}</h3>
              <Badge variant={getRoleBadgeVariant(user.role)}>
                {user.role}
              </Badge>
            </div>
            
            <div className="space-y-1">
              <p className="text-muted-foreground">{user.email}</p>
              {user.phone && (
                <p className="text-sm text-muted-foreground">{user.phone}</p>
              )}
              {user.address && (
                <p className="text-sm text-muted-foreground">{user.address}</p>
              )}
            </div>

            <div className="mt-3 text-xs text-muted-foreground">
              <p>Joined: {new Date(user.createdAt).toLocaleDateString()}</p>
              <p>Last updated: {new Date(user.updatedAt).toLocaleDateString()}</p>
            </div>
          </div>

          {/* Actions */}
          {showActions && (
            <div className="flex flex-col space-y-2">
              <Button size="sm" onClick={() => window.location.href = `/dashboard/admin/users/${user.id}`}>
                View Details
              </Button>
              <Button size="sm" variant="outline">
                Edit
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
