"use client"

import { useState } from "react"
import { User<PERSON><PERSON> } from "./user-card"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dropdown, DropdownItem } from "@/components/ui/dropdown"
import { updateUserRole, deleteUser } from "@/lib/actions/users"
import type { User } from "@/types"

interface UserListProps {
  users: User[]
  showActions?: boolean
}

export function UserList({ users, showActions = true }: UserListProps) {
  const [userList, setUserList] = useState(users)
  const [loadingUsers, setLoadingUsers] = useState<Set<string>>(new Set())

  const handleRoleChange = async (userId: string, newRole: string) => {
    setLoadingUsers(prev => new Set(prev).add(userId))
    
    try {
      const result = await updateUserRole(userId, newRole as 'USER' | 'LIBRARIAN' | 'ADMIN')
      if (result.success) {
        setUserList(prev => 
          prev.map(user => 
            user.id === userId ? { ...user, role: newRole as 'USER' | 'LIBRARIAN' | 'ADMIN' } : user
          )
        )
      }
    } catch (error) {
      console.error('Failed to update user role:', error)
    } finally {
      setLoadingUsers(prev => {
        const newSet = new Set(prev)
        newSet.delete(userId)
        return newSet
      })
    }
  }

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      return
    }

    setLoadingUsers(prev => new Set(prev).add(userId))
    
    try {
      const result = await deleteUser(userId)
      if (result.success) {
        setUserList(prev => prev.filter(user => user.id !== userId))
      }
    } catch (error) {
      console.error('Failed to delete user:', error)
    } finally {
      setLoadingUsers(prev => {
        const newSet = new Set(prev)
        newSet.delete(userId)
        return newSet
      })
    }
  }

  if (!userList.length) {
    return (
      <div className="text-center py-12">
        <svg
          className="mx-auto h-12 w-12 text-muted-foreground"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
          />
        </svg>
        <h3 className="mt-4 text-lg font-semibold">No Users Found</h3>
        <p className="text-muted-foreground">
          No users match your current search criteria.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {userList.map((user) => (
        <Card key={user.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* User Avatar */}
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                  <span className="text-lg font-semibold text-primary">
                    {user.name.charAt(0).toUpperCase()}
                  </span>
                </div>

                {/* User Details */}
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h3 className="text-lg font-semibold">{user.name}</h3>
                    <Badge variant={
                      user.role === 'ADMIN' ? 'destructive' :
                      user.role === 'LIBRARIAN' ? 'default' : 'secondary'
                    }>
                      {user.role}
                    </Badge>
                  </div>
                  <p className="text-muted-foreground">{user.email}</p>
                  {user.phone && (
                    <p className="text-sm text-muted-foreground">{user.phone}</p>
                  )}
                  <p className="text-xs text-muted-foreground mt-1">
                    Joined: {new Date(user.createdAt).toLocaleDateString()}
                  </p>
                </div>
              </div>

              {/* Actions */}
              {showActions && (
                <div className="flex items-center space-x-2">
                  {loadingUsers.has(user.id) ? (
                    <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <>
                      {/* Role Change Dropdown */}
                      <Dropdown
                        trigger={
                          <Button variant="outline" size="sm">
                            Change Role
                          </Button>
                        }
                        align="end"
                      >
                        <DropdownItem 
                          onClick={() => handleRoleChange(user.id, 'USER')}
                          disabled={user.role === 'USER'}
                        >
                          User
                        </DropdownItem>
                        <DropdownItem 
                          onClick={() => handleRoleChange(user.id, 'LIBRARIAN')}
                          disabled={user.role === 'LIBRARIAN'}
                        >
                          Librarian
                        </DropdownItem>
                        <DropdownItem 
                          onClick={() => handleRoleChange(user.id, 'ADMIN')}
                          disabled={user.role === 'ADMIN'}
                        >
                          Admin
                        </DropdownItem>
                      </Dropdown>

                      {/* More Actions */}
                      <Dropdown
                        trigger={
                          <Button variant="ghost" size="sm">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01" />
                            </svg>
                          </Button>
                        }
                        align="end"
                      >
                        <DropdownItem onClick={() => window.location.href = `/dashboard/admin/users/${user.id}`}>
                          View Details
                        </DropdownItem>
                        <DropdownItem onClick={() => handleDeleteUser(user.id)}>
                          Delete User
                        </DropdownItem>
                      </Dropdown>
                    </>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
