"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dropdown, DropdownItem } from "@/components/ui/dropdown"
import { NotificationItem } from "./notification-item"
import { getUserNotifications, markAllNotificationsAsRead } from "@/lib/actions/notifications"
import type { Notification } from "@/types"

interface NotificationBellProps {
  userId: string
  initialNotifications?: Notification[]
}

export function NotificationBell({ userId, initialNotifications = [] }: NotificationBellProps) {
  const [notifications, setNotifications] = useState<Notification[]>(initialNotifications)
  const [isOpen, setIsOpen] = useState(false)
  const [loading, setLoading] = useState(false)

  const unreadCount = notifications.filter(n => !n.read).length

  useEffect(() => {
    const loadNotifications = async () => {
      if (notifications.length === 0) {
        setLoading(true)
        try {
          const result = await getUserNotifications(userId)
          setNotifications(result)
        } catch (error) {
          console.error('Failed to load notifications:', error)
        } finally {
          setLoading(false)
        }
      }
    }

    loadNotifications()
  }, [userId, notifications.length])

  const handleMarkAllAsRead = async () => {
    try {
      await markAllNotificationsAsRead(userId)
      setNotifications(prev => prev.map(n => ({ ...n, read: true })))
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error)
    }
  }

  const handleNotificationUpdate = (notificationId: string, updates: Partial<Notification>) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, ...updates } : n
      )
    )
  }

  const handleNotificationDelete = (notificationId: string) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId))
  }

  return (
    <div className="relative">
      <Dropdown
        trigger={
          <Button variant="ghost" size="icon" className="relative">
            <svg
              className="h-5 w-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h8a2 2 0 012 2v4"
              />
            </svg>
            {unreadCount > 0 && (
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center text-xs p-0"
              >
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
            )}
          </Button>
        }
        align="end"
      >
        <div className="w-80 max-h-96 overflow-hidden">
          <Card className="border-0 shadow-none">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm">Notifications</CardTitle>
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleMarkAllAsRead}
                    className="text-xs"
                  >
                    Mark all read
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {loading ? (
                <div className="p-4 text-center">
                  <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
                  <p className="text-sm text-muted-foreground mt-2">Loading...</p>
                </div>
              ) : notifications.length === 0 ? (
                <div className="p-4 text-center">
                  <svg
                    className="mx-auto h-8 w-8 text-muted-foreground"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h8a2 2 0 012 2v4"
                    />
                  </svg>
                  <p className="text-sm text-muted-foreground mt-2">No notifications</p>
                </div>
              ) : (
                <div className="max-h-64 overflow-y-auto">
                  <div className="space-y-1 p-2">
                    {notifications.slice(0, 5).map((notification) => (
                      <div
                        key={notification.id}
                        className={`p-3 rounded-md cursor-pointer transition-colors ${
                          !notification.read ? 'bg-blue-50 hover:bg-blue-100' : 'hover:bg-muted'
                        }`}
                        onClick={() => {
                          if (!notification.read) {
                            handleNotificationUpdate(notification.id, { read: true })
                          }
                        }}
                      >
                        <div className="flex items-start space-x-2">
                          <div className={`w-2 h-2 rounded-full mt-2 ${
                            !notification.read ? 'bg-blue-500' : 'bg-transparent'
                          }`} />
                          <div className="flex-1 min-w-0">
                            <p className={`text-sm ${!notification.read ? 'font-semibold' : 'font-medium'}`}>
                              {notification.title}
                            </p>
                            <p className="text-xs text-muted-foreground line-clamp-2">
                              {notification.message}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {new Date(notification.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {notifications.length > 5 && (
                    <div className="p-2 border-t">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-full text-xs"
                        onClick={() => window.location.href = '/dashboard/notifications'}
                      >
                        View all notifications
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </Dropdown>
    </div>
  )
}
