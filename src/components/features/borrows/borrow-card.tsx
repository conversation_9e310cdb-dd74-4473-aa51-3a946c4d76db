import * as React from "react"
import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { formatDate, getDaysUntilDue, isOverdue } from "@/lib/utils"
import { cn } from "@/lib/utils"
import type { BorrowWithDetails } from "@/types"

interface BorrowCardProps {
  borrow: BorrowWithDetails
  onReturn?: (borrowId: string) => void
  onRenew?: (borrowId: string) => void
  className?: string
  variant?: 'default' | 'compact'
}

export function BorrowCard({ 
  borrow, 
  onReturn, 
  onRenew, 
  className, 
  variant = 'default' 
}: BorrowCardProps) {
  const daysUntilDue = getDaysUntilDue(borrow.dueDate)
  const overdue = isOverdue(borrow.dueDate)
  
  const getStatusColor = () => {
    if (overdue) return 'danger'
    if (daysUntilDue <= 3) return 'warning'
    return 'success'
  }

  const getStatusText = () => {
    if (overdue) return `Overdue by ${Math.abs(daysUntilDue)} days`
    if (daysUntilDue === 0) return 'Due today'
    if (daysUntilDue === 1) return 'Due tomorrow'
    return `Due in ${daysUntilDue} days`
  }

  return (
    <Card className={cn(
      "transition-all duration-200 hover:shadow-md",
      overdue && "border-destructive",
      className
    )}>
      <CardContent className="p-4">
        <div className="flex space-x-4">
          {/* Book Cover */}
          <div className="flex-shrink-0">
            <div className={cn(
              "relative overflow-hidden rounded bg-muted",
              variant === 'compact' ? "w-12 h-16" : "w-16 h-20"
            )}>
              {borrow.bookCopy.book.coverImageUrl ? (
                <Image
                  src={borrow.bookCopy.book.coverImageUrl}
                  alt={`Cover of ${borrow.bookCopy.book.title}`}
                  fill
                  className="object-cover"
                  sizes="80px"
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <svg
                    className={cn("text-muted-foreground", variant === 'compact' ? "h-6 w-6" : "h-8 w-8")}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                    />
                  </svg>
                </div>
              )}
            </div>
          </div>

          {/* Book Information */}
          <div className="flex-1 min-w-0">
            <div className="space-y-2">
              <div>
                <h3 className={cn(
                  "font-semibold leading-tight",
                  variant === 'compact' ? "text-sm" : "text-base"
                )}>
                  <Link 
                    href={`/books/${borrow.bookCopy.book.id}`}
                    className="hover:text-primary transition-colors"
                  >
                    {borrow.bookCopy.book.title}
                  </Link>
                </h3>
                <p className={cn(
                  "text-muted-foreground",
                  variant === 'compact' ? "text-xs" : "text-sm"
                )}>
                  by {borrow.bookCopy.book.author}
                </p>
              </div>

              {/* Borrow Details */}
              <div className={cn(
                "space-y-1",
                variant === 'compact' ? "text-xs" : "text-sm"
              )}>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Copy:</span>
                  <span className="font-mono">{borrow.bookCopy.copyCode}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Borrowed:</span>
                  <span>{formatDate(borrow.borrowDate)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Due:</span>
                  <Badge variant={getStatusColor()} size="sm">
                    {getStatusText()}
                  </Badge>
                </div>
              </div>

              {/* Actions */}
              {variant !== 'compact' && (
                <div className="flex space-x-2 pt-2">
                  {onReturn && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onReturn(borrow.id)}
                    >
                      Return Book
                    </Button>
                  )}
                  {onRenew && !overdue && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => onRenew(borrow.id)}
                    >
                      Renew
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Due date badge component for reuse
export function DueDateBadge({ dueDate, className }: { dueDate: Date | string; className?: string }) {
  const daysUntilDue = getDaysUntilDue(dueDate)
  const overdue = isOverdue(dueDate)
  
  const getVariant = () => {
    if (overdue) return 'danger'
    if (daysUntilDue <= 3) return 'warning'
    return 'success'
  }

  const getText = () => {
    if (overdue) return `Overdue by ${Math.abs(daysUntilDue)} days`
    if (daysUntilDue === 0) return 'Due today'
    if (daysUntilDue === 1) return 'Due tomorrow'
    return `Due in ${daysUntilDue} days`
  }

  return (
    <Badge variant={getVariant()} className={className}>
      {getText()}
    </Badge>
  )
}
