'use client'

import * as React from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"

interface HeroSectionProps {
  className?: string
}

export function HeroSection({ className }: HeroSectionProps) {
  const router = useRouter()
  const { data: session } = useSession()
  const [searchQuery, setSearchQuery] = React.useState("")

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/books/search?q=${encodeURIComponent(searchQuery.trim())}`)
    }
  }

  return (
    <section className={cn("relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5", className)}>
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />
      
      <div className="relative container mx-auto px-4 py-16 sm:py-24">
        <div className="text-center space-y-8">
          {/* Main Heading */}
          <div className="space-y-4">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight">
              Welcome to Your
              <span className="block text-primary">Digital Library</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Discover, borrow, and manage your favorite books in our comprehensive digital library system. 
              Access thousands of titles with just a few clicks.
            </p>
          </div>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto">
            <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  type="search"
                  placeholder="Search for books, authors, or categories..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="h-12 text-lg"
                  leftIcon={
                    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  }
                />
              </div>
              <Button type="submit" size="lg" className="h-12 px-8">
                Search Books
              </Button>
            </form>
          </div>

          {/* Quick Actions */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            {session ? (
              <>
                <Button size="lg" variant="outline" onClick={() => router.push('/dashboard')}>
                  <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v0" />
                  </svg>
                  Go to Dashboard
                </Button>
                <Button size="lg" variant="outline" onClick={() => router.push('/dashboard/my-books')}>
                  <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  My Books
                </Button>
              </>
            ) : (
              <>
                <Button size="lg" onClick={() => router.push('/auth/signup')}>
                  Get Started
                </Button>
                <Button size="lg" variant="outline" onClick={() => router.push('/auth/signin')}>
                  Sign In
                </Button>
              </>
            )}
            <Button size="lg" variant="ghost" onClick={() => router.push('/books')}>
              Browse All Books
              <svg className="h-4 w-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-4xl mx-auto pt-8">
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-primary">10,000+</div>
              <div className="text-muted-foreground">Books Available</div>
            </div>
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-primary">50+</div>
              <div className="text-muted-foreground">Categories</div>
            </div>
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-primary">1,000+</div>
              <div className="text-muted-foreground">Active Users</div>
            </div>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-1/4 left-10 w-20 h-20 bg-primary/10 rounded-full blur-xl" />
      <div className="absolute bottom-1/4 right-10 w-32 h-32 bg-secondary/10 rounded-full blur-xl" />
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-accent/5 rounded-full blur-2xl" />
    </section>
  )
}

// Quick stats component for reuse
export function QuickStats({ stats }: { stats: { totalBooks: number; totalCategories: number; activeUsers: number } }) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-4xl mx-auto">
      <div className="text-center">
        <div className="text-3xl sm:text-4xl font-bold text-primary">
          {stats.totalBooks.toLocaleString()}
        </div>
        <div className="text-muted-foreground">Books Available</div>
      </div>
      <div className="text-center">
        <div className="text-3xl sm:text-4xl font-bold text-primary">
          {stats.totalCategories}
        </div>
        <div className="text-muted-foreground">Categories</div>
      </div>
      <div className="text-center">
        <div className="text-3xl sm:text-4xl font-bold text-primary">
          {stats.activeUsers.toLocaleString()}
        </div>
        <div className="text-muted-foreground">Active Users</div>
      </div>
    </div>
  )
}
