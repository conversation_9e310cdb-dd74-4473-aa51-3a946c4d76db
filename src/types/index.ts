import { 
  User, 
  Book, 
  BookCopy, 
  Category, 
  BorrowRequest, 
  Borrow, 
  Notification,
  Role,
  BookCondition,
  BookCopyStatus,
  BorrowRequestStatus,
  BorrowStatus,
  NotificationType
} from '@prisma/client';

// Re-export Prisma types
export type {
  User,
  Book,
  BookCopy,
  Category,
  BorrowRequest,
  Borrow,
  Notification,
  Role,
  BookCondition,
  BookCopyStatus,
  BorrowRequestStatus,
  BorrowStatus,
  NotificationType
}

// Extended types with relations
export type BookWithDetails = Book & {
  categories: Category[]
  copies: BookCopy[]
  _count: {
    copies: number
    downloadLogs: number
  }
  hasDigitalCopy: boolean
}

export type BookCopyWithBook = BookCopy & {
  book: Book
}

export type BorrowRequestWithDetails = BorrowRequest & {
  user: User
  bookCopy: BookCopy & {
    book: Book
  }
  processedByUser?: User | null
}

export type BorrowWithDetails = Borrow & {
  user: User
  bookCopy: BookCopy & {
    book: Book
  }
  request?: BorrowRequest | null
}

export type UserWithStats = User & {
  _count: {
    borrows: number
    borrowRequests: number
    notifications: number
  }
}

// Search and filter types
export interface BookSearchParams {
  query?: string
  categories?: string[]
  author?: string
  language?: string
  hasDigitalCopy?: boolean
  availability?: 'available' | 'all'
  sortBy?: 'title' | 'author' | 'year' | 'created'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface BookSearchResult {
  books: BookWithDetails[]
  totalCount: number
  currentPage: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// Form types
export interface CreateBookForm {
  title: string
  author: string
  isbn?: string
  description?: string
  publisher?: string
  publicationYear?: number
  pages?: number
  language?: string
  coverImageUrl?: string
  pdfUrl?: string
  hasDigitalCopy: boolean
  categories: string[]
  copies: {
    copyCode: string
    condition: BookCondition
    location?: string
  }[]
}

export interface CreateUserForm {
  name: string
  email: string
  password: string
  phone?: string
  address?: string
  role: Role
}

export interface LoginForm {
  email: string
  password: string
}

export interface BorrowRequestForm {
  copyId: string
  requestedReturnDate?: Date
}

// Dashboard stats types
export interface DashboardStats {
  totalBooks: number
  totalUsers: number
  activeBorrows: number
  pendingRequests: number
  overdueBooks: number
  availableCopies: number
}

// Notification payload types
export interface NotificationPayload {
  userId: string
  type: NotificationType
  title: string
  message: string
}

// Server action response types
export interface ActionResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Bulk import types
export interface BulkImportBook {
  title: string
  author: string
  isbn?: string
  description?: string
  publisher?: string
  publicationYear?: number
  pages?: number
  language?: string
  categories?: string[]
  copiesCount: number
}

export interface BulkImportResult {
  imported: number
  failed: number
  errors: string[]
} 