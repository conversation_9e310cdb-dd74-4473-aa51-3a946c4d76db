import { withAuth } from "next-auth/middleware"

export default withAuth(
  function middleware(req) {
    // Add any additional middleware logic here
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Allow access to public routes
        if (req.nextUrl.pathname.startsWith('/api/auth')) return true
        if (req.nextUrl.pathname === '/') return true
        if (req.nextUrl.pathname.startsWith('/books')) return true
        if (req.nextUrl.pathname.startsWith('/categories')) return true
        if (req.nextUrl.pathname.startsWith('/auth')) return true
        
        // Require authentication for dashboard routes
        if (req.nextUrl.pathname.startsWith('/dashboard')) {
          return !!token
        }
        
        // Admin routes require admin or librarian role
        if (req.nextUrl.pathname.startsWith('/dashboard/admin')) {
          return token?.role === 'ADMIN' || token?.role === 'LIBRARIAN'
        }
        
        return true
      },
    },
  }
)

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
