{"name": "library", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:seed": "tsx prisma/seed.ts", "db:reset": "npx prisma db push --force-reset && npm run db:seed", "db:test": "DATABASE_URL=\"file:./test.db\" npx prisma db push --force-reset"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@hookform/resolvers": "^5.1.1", "@libsql/client": "^0.15.9", "@prisma/client": "^6.10.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.81.5", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.0.0", "framer-motion": "^12.19.2", "lucide-react": "^0.525.0", "next": "15.3.4", "next-auth": "^4.24.11", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "tailwind-merge": "^3.3.1", "tsx": "^4.20.3", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "tailwindcss": "^4", "ts-jest": "^29.1.2", "typescript": "^5"}, "prisma": {"seed": "tsx prisma/seed.ts"}}