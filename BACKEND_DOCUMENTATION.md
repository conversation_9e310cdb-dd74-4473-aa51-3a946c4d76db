# Library Management System - Backend Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Database Schema](#database-schema)
3. [Authentication System](#authentication-system)
4. [Server Actions API Reference](#server-actions-api-reference)
5. [Type Definitions](#type-definitions)
6. [Error Handling](#error-handling)
7. [Business Rules](#business-rules)
8. [Testing](#testing)

## System Overview

### Tech Stack
- **Framework**: Next.js 14+ with App Router
- **Database**: SQLite with Prisma ORM
- **Authentication**: NextAuth.js with JWT sessions
- **Password Security**: bcrypt with 12 salt rounds
- **Type Safety**: TypeScript with comprehensive type definitions

### Architecture
- **Server Actions**: All backend logic implemented as Next.js server actions
- **Database Client**: Single Prisma client instance with connection pooling
- **Session Management**: JWT-based sessions with role persistence
- **Role-based Access**: USER, ADMIN, LIBRARIAN roles with function-level authorization

## Database Schema

### Core Models

#### User Model
```typescript
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      Role     @default(USER)
  phone     String?
  address   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  borrowRequests    BorrowRequest[] @relation("UserBorrowRequests")
  borrows          Borrow[]
  downloadLogs     DownloadLog[]
  notifications    Notification[]
  processedRequests BorrowRequest[] @relation("ProcessedByUser")
}
```

#### Book Model
```typescript
model Book {
  id               String   @id @default(cuid())
  title            String
  author           String
  isbn             String?  @unique
  description      String?
  publisher        String?
  publicationYear  Int?
  pages            Int?
  language         String?
  coverImageUrl    String?
  pdfUrl           String?
  hasDigitalCopy   Boolean  @default(false)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  
  // Relations
  categories   BookCategory[]
  copies       BookCopy[]
  downloadLogs DownloadLog[]
}
```

#### BookCopy Model
```typescript
model BookCopy {
  id        String          @id @default(cuid())
  bookId    String
  copyCode  String          @unique
  condition BookCondition   @default(GOOD)
  location  String?
  status    BookCopyStatus  @default(AVAILABLE)
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt
  
  // Relations
  book           Book            @relation(fields: [bookId], references: [id], onDelete: Cascade)
  borrowRequests BorrowRequest[]
  borrows        Borrow[]
}
```

#### BorrowRequest Model
```typescript
model BorrowRequest {
  id                  String              @id @default(cuid())
  userId              String
  copyId              String
  requestedReturnDate DateTime?
  status              BorrowRequestStatus @default(PENDING)
  processedBy         String?
  processedAt         DateTime?
  rejectionReason     String?
  createdAt           DateTime            @default(now())
  
  // Relations
  user            User      @relation("UserBorrowRequests", fields: [userId], references: [id], onDelete: Cascade)
  copy            BookCopy  @relation(fields: [copyId], references: [id], onDelete: Cascade)
  processedByUser User?     @relation("ProcessedByUser", fields: [processedBy], references: [id])
  borrows         Borrow[]
}
```

#### Borrow Model
```typescript
model Borrow {
  id         String       @id @default(cuid())
  userId     String
  copyId     String
  requestId  String?
  borrowDate DateTime     @default(now())
  dueDate    DateTime
  returnDate DateTime?
  fineAmount Float        @default(0)
  status     BorrowStatus @default(ACTIVE)
  createdAt  DateTime     @default(now())
  
  // Relations
  user    User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  copy    BookCopy      @relation(fields: [copyId], references: [id], onDelete: Cascade)
  request BorrowRequest? @relation(fields: [requestId], references: [id])
}
```

### Enums

```typescript
enum Role {
  USER
  ADMIN
  LIBRARIAN
}

enum BookCondition {
  EXCELLENT
  GOOD
  FAIR
  POOR
}

enum BookCopyStatus {
  AVAILABLE
  BORROWED
  RESERVED
  MAINTENANCE
  LOST
}

enum BorrowRequestStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
}

enum BorrowStatus {
  ACTIVE
  RETURNED
  OVERDUE
}

enum NotificationType {
  OVERDUE
  APPROVAL
  REJECTION
  REMINDER
  SYSTEM
}
```

## Authentication System

### NextAuth Configuration
- **Provider**: Credentials provider with email/password
- **Session Strategy**: JWT with role persistence
- **Password Security**: bcrypt with 12 salt rounds
- **Custom Pages**: `/login` and `/register`

### Session Structure
```typescript
interface Session {
  user: {
    id: string
    email: string
    name?: string | null
    role: string
  }
}
```

### Helper Functions
```typescript
// Get current user
getCurrentUser(userId: string): Promise<User | null>

// Check role permissions
hasRequiredRole(userRole: string, requiredRoles: string[]): boolean
isAdminOrLibrarian(userRole: string): boolean
```

## Server Actions API Reference

### Book Management (`/lib/actions/books.ts`)

#### `searchBooks(params: BookSearchParams): Promise<BookSearchResult>`
Advanced book search with filtering and pagination.

**Parameters:**
```typescript
interface BookSearchParams {
  query?: string              // Search in title, author, description, ISBN
  categories?: string[]       // Filter by category IDs
  author?: string            // Filter by author
  language?: string          // Filter by language
  hasDigitalCopy?: boolean   // Filter books with digital copies
  availability?: 'available' | 'all'  // Filter by availability
  sortBy?: 'title' | 'author' | 'year' | 'created'
  sortOrder?: 'asc' | 'desc'
  page?: number              // Page number (default: 1)
  limit?: number             // Items per page (default: 12)
}
```

**Returns:**
```typescript
interface BookSearchResult {
  books: BookWithDetails[]
  totalCount: number
  currentPage: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}
```

#### `getBookById(id: string): Promise<BookWithDetails | null>`
Get detailed book information including categories and copies.

#### `createBook(data: CreateBookForm): Promise<ActionResponse>`
Create new book with categories and copies.

**Parameters:**
```typescript
interface CreateBookForm {
  title: string
  author: string
  isbn?: string
  description?: string
  publisher?: string
  publicationYear?: number
  pages?: number
  language?: string
  coverImageUrl?: string
  pdfUrl?: string
  hasDigitalCopy: boolean
  categories: string[]        // Category IDs
  copies: {
    copyCode: string
    condition: BookCondition
    location?: string
  }[]
}
```

#### `updateBook(id: string, data: Partial<CreateBookForm>): Promise<ActionResponse>`
Update existing book. Handles category reassignment.

#### `deleteBook(id: string): Promise<ActionResponse>`
Delete book. Validates no active borrows exist.

#### `addBookCopy(bookId: string, data: BookCopyData): Promise<ActionResponse>`
Add new physical copy to existing book.

#### `updateBookCopy(id: string, data: Partial<BookCopyData>): Promise<ActionResponse>`
Update book copy details.

#### `getFeaturedBooks(): Promise<BookWithDetails[]>`
Get featured books for homepage (recently added).

### Category Management (`/lib/actions/categories.ts`)

#### `getCategories(): Promise<Category[]>`
Get all categories sorted by name.

#### `getCategoryById(id: string): Promise<Category | null>`
Get category by ID.

#### `getCategoryBySlug(slug: string): Promise<Category | null>`
Get category by URL slug.

#### `createCategory(data: {name: string, description?: string}): Promise<ActionResponse>`
Create new category with auto-generated slug.

#### `updateCategory(id: string, data: {name?: string, description?: string}): Promise<ActionResponse>`
Update category. Regenerates slug if name changes.

#### `deleteCategory(id: string): Promise<ActionResponse>`
Delete category. Validates no books are assigned.

### User Management (`/lib/actions/users.ts`)

#### `registerUser(data: CreateUserForm): Promise<ActionResponse>`
Register new user with password hashing.

**Parameters:**
```typescript
interface CreateUserForm {
  name: string
  email: string
  password: string
  phone?: string
  address?: string
  role: Role
}
```

#### `getUserById(id: string): Promise<User | null>`
Get user by ID (password excluded).

#### `updateUserProfile(id: string, data: ProfileData): Promise<ActionResponse>`
Update user profile information.

#### `updateUserPassword(id: string, newPassword: string): Promise<ActionResponse>`
Update user password with hashing.

#### `getUsers(): Promise<UserWithStats[]>`
Get all users with statistics (admin only).

#### `updateUserRole(id: string, role: string): Promise<ActionResponse>`
Update user role (admin only).

#### `deleteUser(id: string): Promise<ActionResponse>`
Delete user. Validates no active borrows.

#### `getUserStats(userId: string): Promise<UserStats | null>`
Get user borrowing statistics.

**Returns:**
```typescript
interface UserStats {
  activeBorrows: number
  totalBorrows: number
  overdueBooks: number
  pendingRequests: number
}
```

### Borrow Management (`/lib/actions/borrows.ts`)

#### `createBorrowRequest(userId: string, data: BorrowRequestForm): Promise<ActionResponse>`
Create new borrow request with validation.

**Business Rules:**
- Maximum 5 active borrows per user
- Copy must be available
- Updates copy status to RESERVED
- Creates notifications for admins/librarians

**Parameters:**
```typescript
interface BorrowRequestForm {
  copyId: string
  requestedReturnDate?: Date
}
```

#### `getUserBorrowRequests(userId: string): Promise<BorrowRequestWithDetails[]>`
Get user's borrow requests with book details.

#### `getPendingBorrowRequests(): Promise<BorrowRequestWithDetails[]>`
Get all pending requests (admin/librarian only).

#### `approveBorrowRequest(requestId: string, processedBy: string): Promise<ActionResponse>`
Approve borrow request and create active borrow.

**Process:**
1. Updates request status to APPROVED
2. Creates Borrow record with 14-day due date
3. Updates copy status to BORROWED
4. Creates approval notification for user

#### `rejectBorrowRequest(requestId: string, processedBy: string, reason?: string): Promise<ActionResponse>`
Reject borrow request.

**Process:**
1. Updates request status to REJECTED
2. Resets copy status to AVAILABLE
3. Creates rejection notification with reason

#### `getUserActiveBorrows(userId: string): Promise<BorrowWithDetails[]>`
Get user's active borrows with book details.

#### `getAllActiveBorrows(): Promise<BorrowWithDetails[]>`
Get all active borrows (admin/librarian only).

#### `returnBook(borrowId: string): Promise<ActionResponse>`
Process book return.

**Process:**
1. Updates borrow status to RETURNED
2. Sets return date
3. Updates copy status to AVAILABLE
4. Creates return notification

#### `updateOverdueBooks(): Promise<ActionResponse>`
Automated function to mark overdue books.

**Process:**
1. Finds borrows past due date
2. Updates status to OVERDUE
3. Creates overdue notifications
4. Should be run daily via cron job

#### `getOverdueBooks(): Promise<BorrowWithDetails[]>`
Get all overdue borrows for reporting.

### Notification Management (`/lib/actions/notifications.ts`)

#### `createNotification(data: NotificationPayload): Promise<ActionResponse>`
Create single notification.

**Parameters:**
```typescript
interface NotificationPayload {
  userId: string
  type: NotificationType
  title: string
  message: string
}
```

#### `getUserNotifications(userId: string): Promise<Notification[]>`
Get user's notifications (newest first).

#### `markNotificationAsRead(id: string): Promise<ActionResponse>`
Mark single notification as read.

#### `markAllNotificationsAsRead(userId: string): Promise<ActionResponse>`
Mark all user notifications as read.

#### `deleteNotification(id: string): Promise<ActionResponse>`
Delete notification.

#### `createBulkNotifications(userIds: string[], data: Omit<NotificationPayload, 'userId'>): Promise<ActionResponse>`
Create notifications for multiple users (system announcements).

### Dashboard & Analytics (`/lib/actions/dashboard.ts`)

#### `getAdminDashboardStats(): Promise<DashboardStats>`
Get comprehensive system statistics.

**Returns:**
```typescript
interface DashboardStats {
  totalBooks: number
  totalUsers: number
  activeBorrows: number
  pendingRequests: number
  overdueBooks: number
  availableCopies: number
}
```

## Type Definitions

### Extended Types with Relations

```typescript
// Book with full details
type BookWithDetails = Book & {
  categories: (Category & { category: Category })[]
  copies: BookCopy[]
  _count: {
    copies: number
    downloadLogs: number
  }
}

// Borrow request with user and book details
type BorrowRequestWithDetails = BorrowRequest & {
  user: User
  copy: BookCopy & {
    book: Book
  }
  processedByUser?: User | null
}

// Active borrow with user and book details
type BorrowWithDetails = Borrow & {
  user: User
  copy: BookCopy & {
    book: Book
  }
  request?: BorrowRequest | null
}

// User with statistics
type UserWithStats = User & {
  _count: {
    borrows: number
    borrowRequests: number
    notifications: number
  }
}
```

### Server Action Response Type

All server actions return a standardized response:

```typescript
interface ActionResponse<T = any> {
  success: boolean
  data?: T              // Returned data on success
  error?: string        // Error message on failure
  message?: string      // Success message
}
```

## Error Handling

### Common Error Patterns

1. **Validation Errors**: Input validation failures
2. **Business Rule Violations**: E.g., borrow limit exceeded
3. **Database Constraints**: Unique constraint violations
4. **Authorization Errors**: Insufficient permissions
5. **Not Found Errors**: Resource doesn't exist

### Error Response Format

```typescript
// Success response
{
  success: true,
  data: {...},
  message: "Operation completed successfully"
}

// Error response
{
  success: false,
  error: "Detailed error message for user"
}
```

## Business Rules

### Borrowing System

1. **Borrow Limits**: Maximum 5 active borrows per user
2. **Loan Period**: 14 days default loan period
3. **Overdue Detection**: Automated daily check for overdue books
4. **Copy Status Flow**: AVAILABLE → RESERVED → BORROWED → AVAILABLE
5. **Request Approval**: Admin/Librarian approval required for all requests

### Book Management

1. **Copy Codes**: Auto-generated unique identifiers (BOOK-XXXX-XX format)
2. **ISBN Uniqueness**: ISBN must be unique across all books
3. **Category Assignment**: Books can have multiple categories
4. **Deletion Rules**: Cannot delete books with active borrows

### User Management

1. **Email Uniqueness**: Email addresses must be unique
2. **Password Security**: Minimum requirements enforced
3. **Role Hierarchy**: ADMIN > LIBRARIAN > USER
4. **Deletion Rules**: Cannot delete users with active borrows

### Notifications

1. **Auto-Creation**: System automatically creates notifications for:
   - Borrow request submissions (to admins)
   - Request approvals/rejections (to users)
   - Overdue books (to users)
   - Book returns (to users)

2. **Retention**: Notifications are kept indefinitely
3. **Bulk Operations**: Support for system-wide announcements

## Testing

### Test Coverage

The backend includes comprehensive test suites for all server actions:

- **Unit Tests**: Individual function testing
- **Integration Tests**: Database interaction testing
- **Business Logic Tests**: Rule validation testing
- **Error Handling Tests**: Error scenario coverage

### Test Database

- **Isolated Environment**: Separate SQLite database for testing
- **Automatic Setup/Teardown**: Database created and destroyed per test suite
- **Seed Data**: Consistent test data generation
- **Mock Functions**: Next.js cache functions mocked

### Running Tests

```bash
# Run all tests
npm test

# Run specific test file
npm test books.test.ts

# Run with coverage
npm test -- --coverage
```

## Utility Functions (`/lib/utils.ts`)

### Password Management
```typescript
hashPassword(password: string): Promise<string>
verifyPassword(password: string, hashedPassword: string): Promise<boolean>
```

### Date Utilities
```typescript
formatDate(date: Date | string): string
formatDateTime(date: Date | string): string
addDaysToDate(date: Date, days: number): Date
isOverdue(dueDate: Date): boolean
```

### String Utilities
```typescript
generateSlug(text: string): string
generateCopyCode(bookTitle: string): string
normalizeSearchTerm(term: string): string
```

### Validation Utilities
```typescript
isValidEmail(email: string): boolean
isValidISBN(isbn: string): boolean
```

---

This documentation serves as the complete reference for all backend functionality. All server actions are fully implemented and tested, providing a robust foundation for frontend development.
