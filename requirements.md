# Library Management System Architecture

## 1. System Overview

### Tech Stack
- **Frontend**: Next.js 14+ (App Router)
- **Backend**: Next.js API Routes
- **Database**: Turso (LibSQL) with Prisma ORM
- **Authentication**: NextAuth.js
- **File Storage**: AWS S3 / Cloudinary for book covers and PDFs
- **Styling**: Tailwind CSS
- **State Management**: Zustand or React Query
- **Email Service**: SendGrid or Resend

## 2. Database Schema

### Core Entities (SQLite Schema)

```sql
-- Users Table
CREATE TABLE users (
  id TEXT PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  name TEXT,
  role TEXT CHECK(role IN ('USER', 'ADMIN', 'LIBRARIAN')) DEFAULT 'USER',
  phone TEXT,
  address TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Categories Table
CREATE TABLE categories (
  id TEXT PRIMARY KEY,
  name TEXT UNIQUE NOT NULL,
  description TEXT,
  slug TEXT UNIQUE NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Books Table (Master book information)
CREATE TABLE books (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  author TEXT NOT NULL,
  isbn TEXT UNIQUE,
  description TEXT,
  publisher TEXT,
  publication_year INTEGER,
  pages INTEGER,
  language TEXT,
  cover_image_url TEXT,
  pdf_url TEXT, -- For downloadable soft copies
  has_digital_copy INTEGER DEFAULT 0, -- SQLite uses INTEGER for boolean
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Book Categories (Many-to-Many)
CREATE TABLE book_categories (
  book_id TEXT NOT NULL,
  category_id TEXT NOT NULL,
  PRIMARY KEY (book_id, category_id),
  FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
);

-- Physical Book Copies
CREATE TABLE book_copies (
  id TEXT PRIMARY KEY,
  book_id TEXT NOT NULL,
  copy_code TEXT UNIQUE NOT NULL, -- Unique identifier for each physical copy
  condition TEXT CHECK(condition IN ('EXCELLENT', 'GOOD', 'FAIR', 'POOR')) DEFAULT 'GOOD',
  location TEXT, -- Shelf location
  status TEXT CHECK(status IN ('AVAILABLE', 'BORROWED', 'RESERVED', 'MAINTENANCE', 'LOST')) DEFAULT 'AVAILABLE',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- Borrow Requests
CREATE TABLE borrow_requests (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  copy_id TEXT NOT NULL,
  request_date DATETIME DEFAULT CURRENT_TIMESTAMP,
  requested_return_date DATE,
  status TEXT CHECK(status IN ('PENDING', 'APPROVED', 'REJECTED', 'CANCELLED')) DEFAULT 'PENDING',
  admin_notes TEXT,
  processed_by TEXT,
  processed_at DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (copy_id) REFERENCES book_copies(id) ON DELETE CASCADE,
  FOREIGN KEY (processed_by) REFERENCES users(id)
);

-- Active Borrows
CREATE TABLE borrows (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  copy_id TEXT NOT NULL,
  request_id TEXT,
  borrow_date DATETIME DEFAULT CURRENT_TIMESTAMP,
  due_date DATE NOT NULL,
  return_date DATETIME,
  fine_amount REAL DEFAULT 0,
  status TEXT CHECK(status IN ('ACTIVE', 'RETURNED', 'OVERDUE')) DEFAULT 'ACTIVE',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (copy_id) REFERENCES book_copies(id) ON DELETE CASCADE,
  FOREIGN KEY (request_id) REFERENCES borrow_requests(id)
);

-- Download Logs (for digital copies)
CREATE TABLE download_logs (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  book_id TEXT NOT NULL,
  download_date DATETIME DEFAULT CURRENT_TIMESTAMP,
  ip_address TEXT,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- Notifications Table
CREATE TABLE notifications (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  type TEXT CHECK(type IN ('OVERDUE', 'APPROVAL', 'REJECTION', 'REMINDER', 'SYSTEM')) NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  read INTEGER DEFAULT 0, -- SQLite uses INTEGER for boolean
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Triggers to prevent borrowing unavailable books
CREATE TRIGGER prevent_invalid_borrow_request
  BEFORE INSERT ON borrow_requests
  FOR EACH ROW
  WHEN (
    SELECT status FROM book_copies WHERE id = NEW.copy_id
  ) NOT IN ('AVAILABLE')
BEGIN
  SELECT RAISE(ABORT, 'Cannot request borrow for unavailable book copy');
END;

-- Trigger to update book copy status when borrow is created
CREATE TRIGGER update_copy_status_on_borrow
  AFTER INSERT ON borrows
  FOR EACH ROW
BEGIN
  UPDATE book_copies 
  SET status = 'BORROWED', updated_at = CURRENT_TIMESTAMP
  WHERE id = NEW.copy_id;
END;

-- Trigger to update book copy status when book is returned
CREATE TRIGGER update_copy_status_on_return
  AFTER UPDATE OF return_date ON borrows
  FOR EACH ROW
  WHEN NEW.return_date IS NOT NULL AND OLD.return_date IS NULL
BEGIN
  UPDATE book_copies 
  SET status = 'AVAILABLE', updated_at = CURRENT_TIMESTAMP
  WHERE id = NEW.copy_id;
END;

-- Indexes for better performance
CREATE INDEX idx_books_title ON books(title);
CREATE INDEX idx_books_author ON books(author);
CREATE INDEX idx_books_isbn ON books(isbn);
CREATE INDEX idx_books_search ON books(title, author, isbn);
CREATE INDEX idx_book_copies_status ON book_copies(status);
CREATE INDEX idx_borrow_requests_status ON borrow_requests(status);
CREATE INDEX idx_borrows_status ON borrows(status);
CREATE INDEX idx_borrows_due_date ON borrows(due_date);
CREATE INDEX idx_notifications_user_read ON notifications(user_id, read);
```

## 3. Application Architecture

### Folder Structure
```
src/
├── app/
│   ├── (auth)/
│   │   ├── login/
│   │   └── register/
│   ├── (dashboard)/
│   │   ├── admin/
│   │   ├── profile/
│   │   └── my-books/
│   ├── books/
│   │   ├── [id]/
│   │   └── category/[slug]/
│   ├── api/
│   │   ├── auth/
│   │   ├── books/
│   │   │   ├── search/
│   │   │   └── route.ts
│   │   ├── borrow/
│   │   ├── admin/
│   │   │   └── books/
│   │   │       └── bulk-import/
│   │   │           └── route.ts
│   │   ├── downloads/
│   │   └── notifications/
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── ui/ (shadcn components)
│   ├── forms/
│   ├── layout/
│   └── features/
├── lib/
│   ├── prisma.ts
│   ├── auth.ts
│   ├── utils.ts
│   ├── search.ts
│   ├── bulk-import.ts
│   └── validations/
├── hooks/
├── store/
├── types/
├── migrations/
│   ├── scripts/
│   │   ├── 001-initial-schema.sql
│   │   ├── 002-add-categories.sql
│   │   ├── 003-add-indexes.sql
│   │   ├── 004-add-notifications.sql
│   │   ├── 005-add-triggers.sql
│   │   └── utils.ts
│   ├── data/
│   │   ├── seed-categories.json
│   │   ├── seed-books.json
│   │   └── seed-users.json
│   └── migrate.ts
└── prisma/
    ├── schema.prisma
    ├── migrations/
    └── seed.ts
```

## 4. Core Features & Pages

### 4.1 Public Pages

#### Homepage
- Featured books carousel
- Book cards should only show the title, and the image. The card should be squarish, not rounded corners
- Category grid
- Search functionality with advanced filters

#### Books Listing
- Filter by categories (multiple selection)
- Advanced search by title, author, ISBN, description
- Sort by title, author, publication year, availability
- Pagination
- Grid/List view toggle

#### Book Detail Page
- Book information (title, author, description, etc.)
- Cover image
- Categories tags
- Physical copies section:
  - List all copies with their unique codes
  - Availability status for each copy
  - "Request Borrow" button for available copies (with validation)
- Digital copy section:
  - Download button
  - File size and format info
- Related books suggestions

#### Category Pages
- Books within specific category
- Category description

### 4.2 Authentication System

#### User Registration/Login
- Email/password authentication
- Email verification
- Password reset functionality

#### User Roles
- **User**: Can browse, request borrows
- **Librarian**: Can manage borrows, view reports
- **Admin**: Full system access, can bulk import books

### 4.3 User Dashboard

#### My Profile
- Personal information management
- Contact details
- Password change

#### My Books
- Active borrows with due dates
- Borrow history
- Outstanding fines
- Renewal requests

#### My Requests
- Pending borrow requests
- Request history with status

#### Notifications
- In-app notification center
- Mark as read/unread functionality
- Filter by notification type

### 4.4 Admin/Librarian Dashboard

#### Books Management
- Add/edit/delete books
- Manage book copies
- Upload book covers and PDF files
- Bulk import functionality (CSV/Excel)

#### Categories Management
- Create/edit/delete categories
- Manage category hierarchies

#### Borrow Management
- View pending requests
- Approve/reject requests (with automatic notifications)
- Manage active borrows
- Handle returns (with automatic status updates)
- Generate overdue reports

#### Users Management
- View user accounts
- Manage user roles
- View user borrow history

#### Reports & Analytics
- Borrowing statistics
- Popular books
- User activity reports
- Fine collection reports

## 5. API Endpoints

### Books API
```
GET /api/books - List books with filters
GET /api/books/search - Advanced search with filters
GET /api/books/[id] - Get book details
POST /api/books - Create book (admin)
PUT /api/books/[id] - Update book (admin)
DELETE /api/books/[id] - Delete book (admin)
GET /api/books/[id]/copies - Get book copies
POST /api/books/[id]/copies - Add book copy (admin)
```

### Borrow API
```
POST /api/borrow/request - Create borrow request (with validation)
GET /api/borrow/requests - Get user`s requests
GET /api/borrow/active - Get active borrows
POST /api/borrow/return - Return book (with status updates)
GET /api/admin/borrow/requests - Get all pending requests (admin)
PUT /api/admin/borrow/requests/[id] - Approve/reject request (admin)
```

### Downloads API
```
GET /api/downloads/[bookId] - Download digital copy
POST /api/downloads/log - Log download activity
```

### Admin API
```
POST /api/admin/books/bulk-import - Bulk import books (CSV/Excel)
GET /api/admin/reports/overdue - Get overdue books report
```

### Notifications API
```
GET /api/notifications - Get user notifications
PUT /api/notifications/[id]/read - Mark notification as read
PUT /api/notifications/mark-all-read - Mark all notifications as read
```

NOTE: THE ARCHITECTURE CONTAINS APIS, BUT THE APP WILL BE BUILT BY USING SERVER ACTIONS FOR ALL OF THEM

## 6. Key Features Implementation

### 6.1 Advanced Search System
- Full-text search using SQLite FTS5 (Full-Text Search)
- Advanced filters (category, availability, language, publication year, etc.)
- Search suggestions and autocomplete
- Recent searches functionality
- Search across title, author, ISBN, and description

### 6.2 Borrow Request Workflow with Validation
1. User selects available book copy
2. System validates copy availability (trigger prevents invalid requests)
3. System creates pending request
4. Admin receives notification
5. Admin approves/rejects with optional notes
6. If approved, borrow record is created with automatic status updates
7. Email and in-app notifications sent to user
8. Due date tracking and overdue notifications

### 6.3 Digital Copy Management
- Secure file upload to cloud storage
- Access control for downloads
- Download logging and analytics
- File format validation
- Some books might be uploaded, or for some books its online copy url would be set
- In case of uploaded books, first store it into cloud, then save the generated url

### 6.4 Notification System
- In-app notifications for:
  - Request approval/rejection
  - Due date reminders
  - Overdue notices
  - System announcements
- Email notifications for critical events
- Notification preferences per user
- Real-time notification updates

### 6.5 Bulk Book Import System
- CSV/Excel file upload support
- Data validation and error reporting
- Batch processing for large files
- Preview functionality before import
- Duplicate detection and handling
- Progress tracking for imports

## 6.6 Overdue Book Management & Notifications

The system will not charge fines. Instead, it relies on a robust notification system to encourage timely returns.

### 6.6.1 Overdue Status Automation
- A scheduled task (cron job) will run daily.
- This job will query the `borrows` table for records where `status` is 'ACTIVE' and the `due_date` is in the past.
- For each record found, the job will update its `status` to 'OVERDUE'.
- **Return Confirmation:** When the book is returned, the borrow `status` is updated to 'RETURNED' with automatic triggers

### 6.6.2 Automated Notification Workflow
- **Overdue Notice:** When a book`s status first changes to 'OVERDUE', an in-app notification is sent to the user.

## 7. Security Considerations

### Authentication & Authorization
- JWT tokens with refresh mechanism
- Role-based access control (RBAC)
- API rate limiting
- Input validation and sanitization

### File Security
- Secure file upload with type validation
- Access control for digital downloads
- Virus scanning for uploaded files
- CDN integration for better performance

### Data Protection
- Password hashing with bcrypt
- Sensitive data encryption
- GDPR compliance features
- Audit logging for admin actions

## 8. Performance Optimization

### Database
- Proper indexing on search fields
- Database connection pooling
- Query optimization
- Caching strategies (Redis for session storage)

### Frontend
- Next.js Image optimization
- Lazy loading for book listings
- Progressive Web App (PWA) features
- SEO optimization

## 10. Turso + Prisma Configuration & Migration Strategy

### Environment Setup
```bash
# Install dependencies
npm install prisma @prisma/client @libsql/client dotenv

# Install Turso CLI
curl -sSfL https://get.tur.so/install.sh | bash

# Create Turso database
turso db create library-system
turso db show library-system
turso db tokens create library-system
```

### Environment Variables
```env
# .env
DATABASE_URL="libsql://[your-database-name]-[your-org].turso.io"
DATABASE_AUTH_TOKEN="your-auth-token"

# .env.local (for development with local replica)
DATABASE_URL="file:dev.db"
DATABASE_AUTH_TOKEN=""
```

Keep the UI simple. So that user's dont get too overwhelmed by them. So dont use shadcn. Might use radix
First start by implementing the entire backend. Use apis as less as possible. Use server side components and server actions in place of apis.

Then after completing the entire backend, implement the entire UI.

Start the project by creating a next app using npx