# Backend Quick Reference Guide

## Most Common Server Actions for Frontend Development

### Authentication & User Management

```typescript
// User Registration
import { registerUser } from '@/lib/actions/users'

const result = await registerUser({
  name: "<PERSON>",
  email: "<EMAIL>", 
  password: "securepassword",
  phone: "+1234567890",
  address: "123 Main St",
  role: "USER"
})

// Get User Profile
import { getUserById } from '@/lib/actions/users'
const user = await getUserById(userId)

// Update Profile
import { updateUserProfile } from '@/lib/actions/users'
const result = await updateUserProfile(userId, {
  name: "Updated Name",
  phone: "+0987654321"
})
```

### Book Search & Display

```typescript
// Search Books (Main Search Function)
import { searchBooks } from '@/lib/actions/books'

const searchResult = await searchBooks({
  query: "javascript",           // Search term
  categories: ["cat1", "cat2"],  // Category filter
  author: "<PERSON>",          // Author filter
  language: "English",           // Language filter
  hasDigitalCopy: true,          // Digital copy filter
  availability: "available",     // Only available books
  sortBy: "title",              // Sort field
  sortOrder: "asc",             // Sort direction
  page: 1,                      // Page number
  limit: 12                     // Items per page
})

// Get Single Book Details
import { getBookById } from '@/lib/actions/books'
const book = await getBookById(bookId)

// Get Featured Books (Homepage)
import { getFeaturedBooks } from '@/lib/actions/books'
const featuredBooks = await getFeaturedBooks()
```

### Categories

```typescript
// Get All Categories (for filters/navigation)
import { getCategories } from '@/lib/actions/categories'
const categories = await getCategories()

// Get Category by Slug (for category pages)
import { getCategoryBySlug } from '@/lib/actions/categories'
const category = await getCategoryBySlug("fiction")
```

### Borrowing System

```typescript
// Create Borrow Request
import { createBorrowRequest } from '@/lib/actions/borrows'

const result = await createBorrowRequest(userId, {
  copyId: "copy123",
  requestedReturnDate: new Date("2024-01-15") // Optional
})

// Get User's Borrow Requests
import { getUserBorrowRequests } from '@/lib/actions/borrows'
const requests = await getUserBorrowRequests(userId)

// Get User's Active Borrows
import { getUserActiveBorrows } from '@/lib/actions/borrows'
const activeBorrows = await getUserActiveBorrows(userId)

// Return Book
import { returnBook } from '@/lib/actions/borrows'
const result = await returnBook(borrowId)
```

### Admin Functions

```typescript
// Get Pending Requests (Admin Dashboard)
import { getPendingBorrowRequests } from '@/lib/actions/borrows'
const pendingRequests = await getPendingBorrowRequests()

// Approve Request
import { approveBorrowRequest } from '@/lib/actions/borrows'
const result = await approveBorrowRequest(requestId, adminUserId)

// Reject Request
import { rejectBorrowRequest } from '@/lib/actions/borrows'
const result = await rejectBorrowRequest(requestId, adminUserId, "Reason for rejection")

// Get Dashboard Stats
import { getAdminDashboardStats } from '@/lib/actions/dashboard'
const stats = await getAdminDashboardStats()

// Get All Active Borrows
import { getAllActiveBorrows } from '@/lib/actions/borrows'
const allBorrows = await getAllActiveBorrows()

// Get All Users
import { getUsers } from '@/lib/actions/users'
const users = await getUsers()
```

### Notifications

```typescript
// Get User Notifications
import { getUserNotifications } from '@/lib/actions/notifications'
const notifications = await getUserNotifications(userId)

// Mark Notification as Read
import { markNotificationAsRead } from '@/lib/actions/notifications'
const result = await markNotificationAsRead(notificationId)

// Mark All as Read
import { markAllNotificationsAsRead } from '@/lib/actions/notifications'
const result = await markAllNotificationsAsRead(userId)
```

### Book Management (Admin)

```typescript
// Create New Book
import { createBook } from '@/lib/actions/books'

const result = await createBook({
  title: "Book Title",
  author: "Author Name",
  isbn: "978-1234567890",
  description: "Book description",
  publisher: "Publisher Name",
  publicationYear: 2024,
  pages: 300,
  language: "English",
  coverImageUrl: "https://example.com/cover.jpg",
  pdfUrl: "https://example.com/book.pdf",
  hasDigitalCopy: true,
  categories: ["categoryId1", "categoryId2"],
  copies: [
    {
      copyCode: "BOOK-001-01",
      condition: "GOOD",
      location: "Shelf A1"
    }
  ]
})

// Update Book
import { updateBook } from '@/lib/actions/books'
const result = await updateBook(bookId, updateData)

// Delete Book
import { deleteBook } from '@/lib/actions/books'
const result = await deleteBook(bookId)

// Add Book Copy
import { addBookCopy } from '@/lib/actions/books'
const result = await addBookCopy(bookId, {
  copyCode: "BOOK-001-02",
  condition: "EXCELLENT",
  location: "Shelf A1"
})
```

## Response Format

All server actions return a standardized response:

```typescript
// Success Response
{
  success: true,
  data: {...},           // The actual data
  message: "Success message"
}

// Error Response  
{
  success: false,
  error: "Error message for user display"
}
```

## Usage in Components

```typescript
// Example React component usage
'use client'

import { useState } from 'react'
import { searchBooks } from '@/lib/actions/books'

export default function BookSearch() {
  const [books, setBooks] = useState([])
  const [loading, setLoading] = useState(false)

  const handleSearch = async (query: string) => {
    setLoading(true)
    try {
      const result = await searchBooks({ query, page: 1, limit: 12 })
      setBooks(result.books)
    } catch (error) {
      console.error('Search failed:', error)
    } finally {
      setLoading(false)
    }
  }

  // Component JSX...
}
```

## Error Handling Best Practices

```typescript
// Always check the success flag
const result = await createBorrowRequest(userId, data)

if (result.success) {
  // Handle success
  toast.success(result.message)
  // Update UI state
} else {
  // Handle error
  toast.error(result.error)
  // Show error to user
}
```

## Common Data Types

```typescript
// Book with full details
interface BookWithDetails {
  id: string
  title: string
  author: string
  isbn?: string
  description?: string
  coverImageUrl?: string
  hasDigitalCopy: boolean
  categories: Array<{category: Category}>
  copies: BookCopy[]
  _count: {
    copies: number
    downloadLogs: number
  }
}

// User with statistics
interface UserWithStats {
  id: string
  email: string
  name?: string
  role: string
  phone?: string
  address?: string
  _count: {
    borrows: number
    borrowRequests: number
    notifications: number
  }
}

// Borrow request with details
interface BorrowRequestWithDetails {
  id: string
  status: string
  createdAt: Date
  requestedReturnDate?: Date
  user: User
  copy: {
    id: string
    copyCode: string
    book: Book
  }
  processedByUser?: User
  rejectionReason?: string
}
```

## Authentication Context

```typescript
// Get current user session
import { useSession } from 'next-auth/react'

const { data: session, status } = useSession()

// Check user role
const isAdmin = session?.user?.role === 'ADMIN'
const isLibrarian = session?.user?.role === 'LIBRARIAN'
const isAdminOrLibrarian = ['ADMIN', 'LIBRARIAN'].includes(session?.user?.role)
```

This quick reference covers the most commonly used server actions for frontend development. All functions are fully implemented and tested.
