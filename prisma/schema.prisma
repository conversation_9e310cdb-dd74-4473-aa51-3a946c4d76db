// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      Role     @default(USER)
  phone     String?
  address   String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  borrowRequests  BorrowRequest[] @relation("UserBorrowRequests")
  borrows         <PERSON>rrow[]
  downloadLogs    DownloadLog[]
  notifications   Notification[]
  processedRequests BorrowRequest[] @relation("ProcessedByUser")

  @@map("users")
}

model Category {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  slug        String   @unique
  createdAt   DateTime @default(now()) @map("created_at")

  // Relations
  books BookCategory[]

  @@map("categories")
}

model Book {
  id               String   @id @default(cuid())
  title            String
  author           String
  isbn             String?  @unique
  description      String?
  publisher        String?
  publicationYear  Int?     @map("publication_year")
  pages            Int?
  language         String?
  coverImageUrl    String?  @map("cover_image_url")
  pdfUrl           String?  @map("pdf_url")
  hasDigitalCopy   Boolean  @default(false) @map("has_digital_copy")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // Relations
  categories   BookCategory[]
  copies       BookCopy[]
  downloadLogs DownloadLog[]

  @@map("books")
}

model BookCategory {
  bookId     String
  categoryId String

  book     Book     @relation(fields: [bookId], references: [id], onDelete: Cascade)
  category Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@id([bookId, categoryId])
  @@map("book_categories")
}

model BookCopy {
  id        String          @id @default(cuid())
  bookId    String          @map("book_id")
  copyCode  String          @unique @map("copy_code")
  condition BookCondition   @default(GOOD)
  location  String?
  status    BookCopyStatus  @default(AVAILABLE)
  createdAt DateTime        @default(now()) @map("created_at")
  updatedAt DateTime        @updatedAt @map("updated_at")

  // Relations
  book           Book            @relation(fields: [bookId], references: [id], onDelete: Cascade)
  borrowRequests BorrowRequest[]
  borrows        Borrow[]

  @@map("book_copies")
}

model BorrowRequest {
  id                   String               @id @default(cuid())
  userId               String               @map("user_id")
  copyId               String               @map("copy_id")
  requestDate          DateTime             @default(now()) @map("request_date")
  requestedReturnDate  DateTime?            @map("requested_return_date")
  status               BorrowRequestStatus  @default(PENDING)
  adminNotes           String?              @map("admin_notes")
  processedBy          String?              @map("processed_by")
  processedAt          DateTime?            @map("processed_at")
  createdAt            DateTime             @default(now()) @map("created_at")

  // Relations
  user        User      @relation("UserBorrowRequests", fields: [userId], references: [id], onDelete: Cascade)
  copy        BookCopy  @relation(fields: [copyId], references: [id], onDelete: Cascade)
  processedByUser User? @relation("ProcessedByUser", fields: [processedBy], references: [id])
  borrows     Borrow[]

  @@map("borrow_requests")
}

model Borrow {
  id         String       @id @default(cuid())
  userId     String       @map("user_id")
  copyId     String       @map("copy_id")
  requestId  String?      @map("request_id")
  borrowDate DateTime     @default(now()) @map("borrow_date")
  dueDate    DateTime     @map("due_date")
  returnDate DateTime?    @map("return_date")
  fineAmount Float        @default(0) @map("fine_amount")
  status     BorrowStatus @default(ACTIVE)
  createdAt  DateTime     @default(now()) @map("created_at")

  // Relations
  user    User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  copy    BookCopy      @relation(fields: [copyId], references: [id], onDelete: Cascade)
  request BorrowRequest? @relation(fields: [requestId], references: [id])

  @@map("borrows")
}

model DownloadLog {
  id           String   @id @default(cuid())
  userId       String   @map("user_id")
  bookId       String   @map("book_id")
  downloadDate DateTime @default(now()) @map("download_date")
  ipAddress    String?  @map("ip_address")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  book Book @relation(fields: [bookId], references: [id], onDelete: Cascade)

  @@map("download_logs")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String           @map("user_id")
  type      NotificationType
  title     String
  message   String
  read      Boolean          @default(false)
  createdAt DateTime         @default(now()) @map("created_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

// Enums
enum Role {
  USER
  ADMIN
  LIBRARIAN
}

enum BookCondition {
  EXCELLENT
  GOOD
  FAIR
  POOR
}

enum BookCopyStatus {
  AVAILABLE
  BORROWED
  RESERVED
  MAINTENANCE
  LOST
}

enum BorrowRequestStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
}

enum BorrowStatus {
  ACTIVE
  RETURNED
  OVERDUE
}

enum NotificationType {
  OVERDUE
  APPROVAL
  REJECTION
  REMINDER
  SYSTEM
} 