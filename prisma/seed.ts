import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12)
}

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create admin user
  const adminPassword = await hashPassword('admin123')
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Library Admin',
      password: adminPassword,
      role: 'ADMIN',
      phone: '******-0001',
      address: '123 Admin Street, Library City'
    }
  })
  console.log('✅ Admin user created')

  // Create librarian user
  const librarianPassword = await hashPassword('librarian123')
  const librarian = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON> Librarian',
      password: librarianPassword,
      role: 'LIB<PERSON>RI<PERSON>',
      phone: '******-0002',
      address: '456 Librarian Ave, Library City'
    }
  })
  console.log('✅ <PERSON>brarian user created')

  // Create sample regular users
  const userPassword = await hashPassword('user123')
  const users = []
  for (let i = 1; i <= 5; i++) {
    const user = await prisma.user.upsert({
      where: { email: `user${i}@example.com` },
      update: {},
      create: {
        email: `user${i}@example.com`,
        name: `User ${i}`,
        password: userPassword,
        role: 'USER',
        phone: `******-000${i + 2}`,
        address: `${100 + i} User Street, Library City`
      }
    })
    users.push(user)
  }
  console.log('✅ Sample users created')

  // Create categories
  const categories = [
    { name: 'Fiction', description: 'Novels, short stories, and fictional works', slug: 'fiction' },
    { name: 'Non-Fiction', description: 'Factual books, biographies, and educational content', slug: 'non-fiction' },
    { name: 'Science', description: 'Scientific research, theories, and discoveries', slug: 'science' },
    { name: 'Technology', description: 'Computing, programming, and technological advances', slug: 'technology' },
    { name: 'History', description: 'Historical events, periods, and figures', slug: 'history' },
    { name: 'Biography', description: 'Life stories and memoirs', slug: 'biography' },
    { name: 'Self-Help', description: 'Personal development and improvement', slug: 'self-help' },
    { name: 'Business', description: 'Business strategy, management, and entrepreneurship', slug: 'business' },
    { name: 'Arts', description: 'Visual arts, music, and creative expression', slug: 'arts' },
    { name: 'Philosophy', description: 'Philosophical thoughts and theories', slug: 'philosophy' }
  ]

  const createdCategories = []
  for (const categoryData of categories) {
    const category = await prisma.category.upsert({
      where: { slug: categoryData.slug },
      update: {},
      create: categoryData
    })
    createdCategories.push(category)
  }
  console.log('✅ Categories created')

  // Create sample books
  const books = [
    {
      title: 'The Great Gatsby',
      author: 'F. Scott Fitzgerald',
      isbn: '9780743273565',
      description: 'A classic American novel set in the Jazz Age, exploring themes of wealth, love, and the American Dream.',
      publisher: 'Scribner',
      publicationYear: 1925,
      pages: 180,
      language: 'English',
      hasDigitalCopy: true,
      categories: ['Fiction'],
      copiesCount: 3
    },
    {
      title: 'To Kill a Mockingbird',
      author: 'Harper Lee',
      isbn: '9780061120084',
      description: 'A gripping tale of racial injustice and childhood innocence in the American South.',
      publisher: 'HarperCollins',
      publicationYear: 1960,
      pages: 376,
      language: 'English',
      hasDigitalCopy: true,
      categories: ['Fiction'],
      copiesCount: 2
    },
    {
      title: 'A Brief History of Time',
      author: 'Stephen Hawking',
      isbn: '9780553380163',
      description: 'An exploration of cosmology and the nature of time and space.',
      publisher: 'Bantam Books',
      publicationYear: 1988,
      pages: 256,
      language: 'English',
      hasDigitalCopy: true,
      categories: ['Science', 'Non-Fiction'],
      copiesCount: 2
    },
    {
      title: 'Clean Code',
      author: 'Robert C. Martin',
      isbn: '9780132350884',
      description: 'A handbook of agile software craftsmanship for writing clean, maintainable code.',
      publisher: 'Prentice Hall',
      publicationYear: 2008,
      pages: 464,
      language: 'English',
      hasDigitalCopy: true,
      categories: ['Technology', 'Non-Fiction'],
      copiesCount: 4
    },
    {
      title: 'The Art of War',
      author: 'Sun Tzu',
      isbn: '9781599869773',
      description: 'Ancient Chinese military treatise on strategy and tactics.',
      publisher: 'Various',
      publicationYear: -500,
      pages: 273,
      language: 'English',
      hasDigitalCopy: false,
      categories: ['History', 'Philosophy'],
      copiesCount: 2
    },
    {
      title: 'Steve Jobs',
      author: 'Walter Isaacson',
      isbn: '9781451648539',
      description: 'The exclusive biography of Apple co-founder Steve Jobs.',
      publisher: 'Simon & Schuster',
      publicationYear: 2011,
      pages: 656,
      language: 'English',
      hasDigitalCopy: true,
      categories: ['Biography', 'Business'],
      copiesCount: 3
    },
    {
      title: 'The 7 Habits of Highly Effective People',
      author: 'Stephen R. Covey',
      isbn: '9780743269513',
      description: 'A comprehensive guide to personal and professional effectiveness.',
      publisher: 'Free Press',
      publicationYear: 1989,
      pages: 381,
      language: 'English',
      hasDigitalCopy: true,
      categories: ['Self-Help', 'Business'],
      copiesCount: 3
    },
    {
      title: 'Sapiens: A Brief History of Humankind',
      author: 'Yuval Noah Harari',
      isbn: '9780062316097',
      description: 'An exploration of human history from the Stone Age to the present.',
      publisher: 'Harper',
      publicationYear: 2014,
      pages: 443,
      language: 'English',
      hasDigitalCopy: true,
      categories: ['History', 'Non-Fiction'],
      copiesCount: 4
    },
    {
      title: 'The Lean Startup',
      author: 'Eric Ries',
      isbn: '9780307887894',
      description: 'How constant innovation creates radically successful businesses.',
      publisher: 'Crown Business',
      publicationYear: 2011,
      pages: 336,
      language: 'English',
      hasDigitalCopy: true,
      categories: ['Business', 'Technology'],
      copiesCount: 2
    },
    {
      title: 'The Catcher in the Rye',
      author: 'J.D. Salinger',
      isbn: '9780316769174',
      description: 'A controversial novel about teenage rebellion and alienation.',
      publisher: 'Little, Brown and Company',
      publicationYear: 1951,
      pages: 277,
      language: 'English',
      hasDigitalCopy: false,
      categories: ['Fiction'],
      copiesCount: 2
    }
  ]

  for (const bookData of books) {
    // Find category IDs
    const bookCategories = createdCategories.filter(cat => 
      bookData.categories.includes(cat.name)
    )

    const book = await prisma.book.upsert({
      where: { isbn: bookData.isbn },
      update: {},
      create: {
        title: bookData.title,
        author: bookData.author,
        isbn: bookData.isbn,
        description: bookData.description,
        publisher: bookData.publisher,
        publicationYear: bookData.publicationYear,
        pages: bookData.pages,
        language: bookData.language,
        hasDigitalCopy: bookData.hasDigitalCopy,
        categories: {
          create: bookCategories.map(category => ({
            categoryId: category.id
          }))
        },
        copies: {
          create: Array.from({ length: bookData.copiesCount }, (_, index) => ({
            copyCode: `${bookData.title.split(' ').map(word => word.charAt(0).toUpperCase()).join('').slice(0, 3)}-${String(index + 1).padStart(3, '0')}`,
            condition: 'GOOD',
            location: `Shelf ${Math.floor(Math.random() * 20) + 1}`,
            status: 'AVAILABLE'
          }))
        }
      }
    })
  }
  console.log('✅ Sample books created')

  // Create some sample borrow requests and borrows
  const sampleBooks = await prisma.book.findMany({
    include: { copies: true }
  })

  // Create a few borrow requests for demonstration
  if (sampleBooks.length > 0 && users.length > 0) {
    // Create approved borrow (active)
    const borrowRequest1 = await prisma.borrowRequest.create({
      data: {
        userId: users[0].id,
        copyId: sampleBooks[0].copies[0].id,
        status: 'APPROVED',
        processedBy: admin.id,
        processedAt: new Date(),
        adminNotes: 'Approved for regular borrower'
      }
    })

    const dueDate = new Date()
    dueDate.setDate(dueDate.getDate() + 14)

    await prisma.borrow.create({
      data: {
        userId: users[0].id,
        copyId: sampleBooks[0].copies[0].id,
        requestId: borrowRequest1.id,
        dueDate: dueDate
      }
    })

    // Update copy status
    await prisma.bookCopy.update({
      where: { id: sampleBooks[0].copies[0].id },
      data: { status: 'BORROWED' }
    })

    // Create pending request
    await prisma.borrowRequest.create({
      data: {
        userId: users[1].id,
        copyId: sampleBooks[1].copies[0].id,
        status: 'PENDING'
      }
    })

    // Update copy status to reserved
    await prisma.bookCopy.update({
      where: { id: sampleBooks[1].copies[0].id },
      data: { status: 'RESERVED' }
    })

    console.log('✅ Sample borrow requests and borrows created')
  }

  // Create sample notifications
  for (const user of users.slice(0, 3)) {
    await prisma.notification.create({
      data: {
        userId: user.id,
        type: 'SYSTEM',
        title: 'Welcome to the Library',
        message: 'Welcome to our digital library system! You can now browse and request books online.',
        read: false
      }
    })
  }

  // Create notification for admin about pending request
  await prisma.notification.create({
    data: {
      userId: admin.id,
      type: 'SYSTEM',
      title: 'New Borrow Request',
      message: 'A new borrow request is pending approval.',
      read: false
    }
  })

  console.log('✅ Sample notifications created')

  console.log('🎉 Database seeding completed successfully!')
  console.log('')
  console.log('📋 Login credentials:')
  console.log('Admin: <EMAIL> / admin123')
  console.log('Librarian: <EMAIL> / librarian123')
  console.log('User: <EMAIL> / user123 (or <EMAIL>)')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 