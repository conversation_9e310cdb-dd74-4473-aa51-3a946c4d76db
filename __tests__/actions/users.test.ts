import { 
  registerUser, 
  getUserById, 
  updateUser<PERSON>rofile, 
  updateUserPassword, 
  getUsers, 
  updateUserRole, 
  deleteUser, 
  getUserStats 
} from '@/lib/actions/users'
import { verifyPassword } from '@/lib/utils'
import { 
  createTestUser, 
  createTestAdmin, 
  generateRandomEmail,
  createTestBorrow,
  createTestBorrowRequest,
  createTestNotification,
  createTestBook,
  createTestBookCopy,
  mockConsole
} from '../utils/test-helpers'
import { prisma } from '../setup/test-setup'

mockConsole()

describe('User Actions', () => {
  describe('registerUser', () => {
    it('should create a new user successfully', async () => {
      const userData = {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'password123',
        phone: '************',
        address: '123 Main St',
        role: 'USER' as const
      }

      const result = await registerUser(userData)

      expect(result.success).toBe(true)
      expect(result.data).toMatchObject({
        name: userData.name,
        email: userData.email,
        role: userData.role,
        phone: userData.phone,
        address: userData.address
      })
      expect(result.data?.password).toBeUndefined()
      expect(result.message).toBe('User registered successfully')

      // Verify user exists in database
      const user = await prisma.user.findUnique({
        where: { email: userData.email }
      })
      expect(user).toBeTruthy()
      expect(await verifyPassword(userData.password, user!.password)).toBe(true)
    })

    it('should fail when user with email already exists', async () => {
      const email = generateRandomEmail()
      await createTestUser({ email })

      const result = await registerUser({
        name: 'Jane Doe',
        email,
        password: 'password123',
        role: 'USER'
      })

      expect(result.success).toBe(false)
      expect(result.error).toBe('User with this email already exists')
    })

    it('should handle database errors gracefully', async () => {
      // Mock prisma to throw an error
      const originalCreate = prisma.user.create
      prisma.user.create = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await registerUser({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        role: 'USER'
      })

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to register user')

      // Restore original function
      prisma.user.create = originalCreate
    })
  })

  describe('getUserById', () => {
    it('should return user by ID without password', async () => {
      const user = await createTestUser()

      const result = await getUserById(user.id)

      expect(result).toMatchObject({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role
      })
      expect(result?.password).toBeUndefined()
    })

    it('should return null for non-existent user', async () => {
      const result = await getUserById('non-existent-id')
      expect(result).toBeNull()
    })

    it('should handle database errors gracefully', async () => {
      const originalFindUnique = prisma.user.findUnique
      prisma.user.findUnique = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await getUserById('some-id')

      expect(result).toBeNull()
      expect(console.error).toHaveBeenCalled()

      prisma.user.findUnique = originalFindUnique
    })
  })

  describe('updateUserProfile', () => {
    it('should update user profile successfully', async () => {
      const user = await createTestUser()
      const updateData = {
        name: 'Updated Name',
        phone: '************',
        address: '456 Updated St'
      }

      const result = await updateUserProfile(user.id, updateData)

      expect(result.success).toBe(true)
      expect(result.data).toMatchObject(updateData)
      expect(result.message).toBe('Profile updated successfully')

      // Verify update in database
      const updatedUser = await prisma.user.findUnique({
        where: { id: user.id }
      })
      expect(updatedUser).toMatchObject(updateData)
    })

    it('should handle partial updates', async () => {
      const user = await createTestUser()
      const updateData = { name: 'Only Name Updated' }

      const result = await updateUserProfile(user.id, updateData)

      expect(result.success).toBe(true)
      expect(result.data?.name).toBe(updateData.name)
    })

    it('should handle database errors gracefully', async () => {
      const user = await createTestUser()
      const originalUpdate = prisma.user.update
      prisma.user.update = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await updateUserProfile(user.id, { name: 'New Name' })

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to update profile')

      prisma.user.update = originalUpdate
    })
  })

  describe('updateUserPassword', () => {
    it('should update user password successfully', async () => {
      const user = await createTestUser()
      const newPassword = 'newPassword123'

      const result = await updateUserPassword(user.id, newPassword)

      expect(result.success).toBe(true)
      expect(result.message).toBe('Password updated successfully')

      // Verify password was updated
      const updatedUser = await prisma.user.findUnique({
        where: { id: user.id }
      })
      expect(await verifyPassword(newPassword, updatedUser!.password)).toBe(true)
    })

    it('should handle database errors gracefully', async () => {
      const user = await createTestUser()
      const originalUpdate = prisma.user.update
      prisma.user.update = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await updateUserPassword(user.id, 'newPassword')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to update password')

      prisma.user.update = originalUpdate
    })
  })

  describe('getUsers', () => {
    it('should return all users with stats', async () => {
      const user1 = await createTestUser({ email: '<EMAIL>' })
      const user2 = await createTestUser({ email: '<EMAIL>' })

      const result = await getUsers()

      expect(result).toHaveLength(2)
      expect(result[0]).toHaveProperty('_count')
      expect(result[0]._count).toHaveProperty('borrows')
      expect(result[0]._count).toHaveProperty('borrowRequests')
      expect(result[0]._count).toHaveProperty('notifications')
      expect(result[0].password).toBeUndefined()
    })

    it('should return users ordered by creation date (newest first)', async () => {
      const user1 = await createTestUser({ email: '<EMAIL>' })
      // Small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10))
      const user2 = await createTestUser({ email: '<EMAIL>' })

      const result = await getUsers()

      expect(result[0].id).toBe(user2.id) // Newest first
      expect(result[1].id).toBe(user1.id)
    })

    it('should handle database errors gracefully', async () => {
      const originalFindMany = prisma.user.findMany
      prisma.user.findMany = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await getUsers()

      expect(result).toEqual([])
      expect(console.error).toHaveBeenCalled()

      prisma.user.findMany = originalFindMany
    })
  })

  describe('updateUserRole', () => {
    it('should update user role successfully', async () => {
      const user = await createTestUser()
      const newRole = 'ADMIN'

      const result = await updateUserRole(user.id, newRole)

      expect(result.success).toBe(true)
      expect(result.data?.role).toBe(newRole)
      expect(result.message).toBe('User role updated successfully')

      // Verify role update in database
      const updatedUser = await prisma.user.findUnique({
        where: { id: user.id }
      })
      expect(updatedUser?.role).toBe(newRole)
    })

    it('should handle database errors gracefully', async () => {
      const user = await createTestUser()
      const originalUpdate = prisma.user.update
      prisma.user.update = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await updateUserRole(user.id, 'ADMIN')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to update user role')

      prisma.user.update = originalUpdate
    })
  })

  describe('deleteUser', () => {
    it('should delete user successfully when no active borrows', async () => {
      const user = await createTestUser()

      const result = await deleteUser(user.id)

      expect(result.success).toBe(true)
      expect(result.message).toBe('User deleted successfully')

      // Verify user was deleted
      const deletedUser = await prisma.user.findUnique({
        where: { id: user.id }
      })
      expect(deletedUser).toBeNull()
    })

    it('should fail to delete user with active borrows', async () => {
      const user = await createTestUser()
      const book = await createTestBook()
      const copy = await createTestBookCopy(book.id)
      await createTestBorrow(user.id, copy.id, { status: 'ACTIVE' })

      const result = await deleteUser(user.id)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Cannot delete user with active borrows')

      // Verify user still exists
      const existingUser = await prisma.user.findUnique({
        where: { id: user.id }
      })
      expect(existingUser).toBeTruthy()
    })

    it('should handle database errors gracefully', async () => {
      const user = await createTestUser()
      const originalDelete = prisma.user.delete
      prisma.user.delete = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await deleteUser(user.id)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to delete user')

      prisma.user.delete = originalDelete
    })
  })

  describe('getUserStats', () => {
    it('should return correct user statistics', async () => {
      const user = await createTestUser()
      const book = await createTestBook()
      const copy1 = await createTestBookCopy(book.id)
      const copy2 = await createTestBookCopy(book.id, { copyCode: 'COPY-002' })

      // Create test data
      await createTestBorrow(user.id, copy1.id, { status: 'ACTIVE' })
      await createTestBorrow(user.id, copy2.id, { status: 'OVERDUE' })
      await createTestBorrow(user.id, copy1.id, { status: 'RETURNED' })
      await createTestBorrowRequest(user.id, copy2.id, { status: 'PENDING' })

      const result = await getUserStats(user.id)

      expect(result).toEqual({
        activeBorrows: 1,
        totalBorrows: 3,
        overdueBooks: 1,
        pendingRequests: 1
      })
    })

    it('should return zero stats for user with no activity', async () => {
      const user = await createTestUser()

      const result = await getUserStats(user.id)

      expect(result).toEqual({
        activeBorrows: 0,
        totalBorrows: 0,
        overdueBooks: 0,
        pendingRequests: 0
      })
    })

    it('should handle database errors gracefully', async () => {
      const user = await createTestUser()
      const originalCount = prisma.borrow.count
      prisma.borrow.count = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await getUserStats(user.id)

      expect(result).toBeNull()
      expect(console.error).toHaveBeenCalled()

      prisma.borrow.count = originalCount
    })
  })
}) 