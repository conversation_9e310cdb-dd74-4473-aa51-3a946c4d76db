import {
  getAdminDashboardStats,
  getPopularBooks,
  getRecentActivities,
  getBorrowingTrends,
  getCategoryDistribution,
  getUserEngagementStats,
  getOverdueAnalysis
} from '@/lib/actions/dashboard'
import {
  createTestUser,
  createTestBook,
  createTestBookCopy,
  createTestCategory,
  createTestBorrow,
  createTestBorrowRequest,
  mockConsole
} from '../utils/test-helpers'
import { prisma } from '../setup/test-setup'

// Mock Next.js functions
jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
  revalidateTag: jest.fn(),
}))

// Setup console mocking
mockConsole()

describe('Dashboard Actions', () => {
  describe('getAdminDashboardStats', () => {
    it('should return correct dashboard statistics', async () => {
      // Create test data
      const user = await createTestUser()
      const book = await createTestBook()
      const copy = await createTestBookCopy(book.id, { status: 'AVAILABLE' })
      
      await createTestBorrow(user.id, copy.id, { status: 'ACTIVE' })
      await createTestBorrowRequest(user.id, copy.id, { status: 'PENDING' })

      const stats = await getAdminDashboardStats()

      expect(stats.totalBooks).toBe(1)
      expect(stats.totalUsers).toBe(1)
      expect(stats.activeBorrows).toBe(1)
      expect(stats.pendingRequests).toBe(1)
      expect(stats.overdueBooks).toBe(0)
      expect(stats.availableCopies).toBe(1)
    })

    it('should return zero stats when no data exists', async () => {
      const stats = await getAdminDashboardStats()

      expect(stats.totalBooks).toBe(0)
      expect(stats.totalUsers).toBe(0)
      expect(stats.activeBorrows).toBe(0)
      expect(stats.pendingRequests).toBe(0)
      expect(stats.overdueBooks).toBe(0)
      expect(stats.availableCopies).toBe(0)
    })

    it('should handle database errors gracefully', async () => {
      const originalCount = prisma.book.count
      prisma.book.count = jest.fn().mockRejectedValue(new Error('Database error'))

      const stats = await getAdminDashboardStats()

      expect(stats.totalBooks).toBe(0)
      expect(stats.totalUsers).toBe(0)
      expect(stats.activeBorrows).toBe(0)
      expect(stats.pendingRequests).toBe(0)
      expect(stats.overdueBooks).toBe(0)
      expect(stats.availableCopies).toBe(0)

      prisma.book.count = originalCount
    })
  })

  describe('getPopularBooks', () => {
    it('should return popular books based on download logs', async () => {
      const user = await createTestUser()
      const book = await createTestBook({ title: 'Popular Book' })
      
      // Create download logs to simulate popularity
      await prisma.downloadLog.create({
        data: { userId: user.id, bookId: book.id }
      })

      const result = await getPopularBooks(5)

      expect(Array.isArray(result)).toBe(true)
      if (result.length > 0) {
        expect(result[0]).toHaveProperty('title')
        expect(result[0]).toHaveProperty('copies')
        expect(result[0]).toHaveProperty('categories')
        expect(result[0]).toHaveProperty('_count')
      }
    })

    it('should return empty array when no books exist', async () => {
      const result = await getPopularBooks()

      expect(result).toEqual([])
    })

    it('should handle database errors gracefully', async () => {
      const originalFindMany = prisma.book.findMany
      prisma.book.findMany = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await getPopularBooks()

      expect(result).toEqual([])

      prisma.book.findMany = originalFindMany
    })
  })

  describe('getRecentActivities', () => {
    it('should return recent activities', async () => {
      const user = await createTestUser()
      const book = await createTestBook()
      const copy = await createTestBookCopy(book.id)
      
      await createTestBorrow(user.id, copy.id, { status: 'ACTIVE' })

      const result = await getRecentActivities(10)

      expect(Array.isArray(result)).toBe(true)
      if (result.length > 0) {
        expect(result[0]).toHaveProperty('id')
        expect(result[0]).toHaveProperty('type')
        expect(result[0]).toHaveProperty('user')
        expect(result[0]).toHaveProperty('book')
        expect(result[0]).toHaveProperty('date')
      }
    })

    it('should return empty array when no activities exist', async () => {
      const result = await getRecentActivities()

      expect(result).toEqual([])
    })

    it('should handle database errors gracefully', async () => {
      const originalFindMany = prisma.borrow.findMany
      prisma.borrow.findMany = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await getRecentActivities()

      expect(result).toEqual([])

      prisma.borrow.findMany = originalFindMany
    })
  })

  describe('getBorrowingTrends', () => {
    it('should return borrowing trends for last 12 months', async () => {
      const user = await createTestUser()
      const book = await createTestBook()
      const copy = await createTestBookCopy(book.id)
      
      // Create a borrow to generate some data
      await createTestBorrow(user.id, copy.id, { status: 'ACTIVE' })

      const trends = await getBorrowingTrends()

      expect(Array.isArray(trends)).toBe(true)
      expect(trends).toHaveLength(12) // Should return 12 months
      
      if (trends.length > 0) {
        expect(trends[0]).toHaveProperty('month')
        expect(trends[0]).toHaveProperty('count')
        expect(typeof trends[0].count).toBe('number')
      }
    })

    it('should return 12 months with zero counts when no borrowing data exists', async () => {
      const trends = await getBorrowingTrends()

      expect(trends).toHaveLength(12)
      expect(trends.every(trend => trend.count === 0)).toBe(true)
    })

    it('should handle database errors gracefully', async () => {
      const originalFindMany = prisma.borrow.findMany
      prisma.borrow.findMany = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await getBorrowingTrends()

      expect(result).toEqual([])

      prisma.borrow.findMany = originalFindMany
    })
  })

  describe('getCategoryDistribution', () => {
    it('should return category distribution with book counts', async () => {
      const category1 = await createTestCategory({ name: 'Science' })
      const category2 = await createTestCategory({ name: 'Fiction' })
      
      const book1 = await createTestBook()
      const book2 = await createTestBook()
      const book3 = await createTestBook()
      
      await prisma.bookCategory.createMany({
        data: [
          { bookId: book1.id, categoryId: category1.id },
          { bookId: book2.id, categoryId: category1.id },
          { bookId: book3.id, categoryId: category2.id }
        ]
      })

      const distribution = await getCategoryDistribution()

      expect(Array.isArray(distribution)).toBe(true)
      expect(distribution.length).toBe(2)
      
      const scienceCategory = distribution.find(cat => cat.name === 'Science')
      const fictionCategory = distribution.find(cat => cat.name === 'Fiction')
      
      expect(scienceCategory?.count).toBe(2)
      expect(fictionCategory?.count).toBe(1)
    })

    it('should return empty array when no categories exist', async () => {
      const distribution = await getCategoryDistribution()

      expect(distribution).toEqual([])
    })

    it('should handle database errors gracefully', async () => {
      const originalFindMany = prisma.category.findMany
      prisma.category.findMany = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await getCategoryDistribution()

      expect(result).toEqual([])

      prisma.category.findMany = originalFindMany
    })
  })

  describe('getUserEngagementStats', () => {
    it('should return user engagement statistics', async () => {
      const user1 = await createTestUser()
      const user2 = await createTestUser()
      const book = await createTestBook()
      const copy = await createTestBookCopy(book.id)
      
      // Create borrows for different users
      await createTestBorrow(user1.id, copy.id, { status: 'ACTIVE' })
      await createTestBorrow(user1.id, copy.id, { status: 'RETURNED' })
      await createTestBorrow(user2.id, copy.id, { status: 'ACTIVE' })

      const stats = await getUserEngagementStats()

      expect(typeof stats.totalUsers).toBe('number')
      expect(typeof stats.activeUsers).toBe('number')
      expect(typeof stats.usersWithBorrows).toBe('number')
      expect(typeof stats.usersWithRequests).toBe('number')
      expect(typeof stats.averageBorrowsPerUser).toBe('number')
      expect(stats.totalUsers).toBe(2)
      expect(stats.activeUsers).toBeGreaterThan(0)
      expect(stats.averageBorrowsPerUser).toBeGreaterThan(0)
    })

    it('should handle case with no users', async () => {
      const stats = await getUserEngagementStats()

      expect(stats.totalUsers).toBe(0)
      expect(stats.activeUsers).toBe(0)
      expect(stats.usersWithBorrows).toBe(0)
      expect(stats.usersWithRequests).toBe(0)
      expect(stats.averageBorrowsPerUser).toBe(0)
    })

    it('should handle database errors gracefully', async () => {
      const originalCount = prisma.user.count
      prisma.user.count = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await getUserEngagementStats()

      expect(result).toEqual({
        totalUsers: 0,
        activeUsers: 0,
        usersWithBorrows: 0,
        usersWithRequests: 0,
        averageBorrowsPerUser: 0
      })

      prisma.user.count = originalCount
    })
  })

  describe('getOverdueAnalysis', () => {
    it('should return overdue analysis with correct data', async () => {
      const user = await createTestUser()
      const book = await createTestBook()
      const copy = await createTestBookCopy(book.id)
      
      // Create overdue borrow
      const pastDate = new Date()
      pastDate.setDate(pastDate.getDate() - 10) // 10 days ago
      
      await createTestBorrow(user.id, copy.id, {
        status: 'OVERDUE',
        dueDate: pastDate
      })

      const analysis = await getOverdueAnalysis()

      expect(typeof analysis.totalOverdue).toBe('number')
      expect(analysis).toHaveProperty('overdueByDays')
      expect(analysis.overdueByDays).toHaveProperty('1-7')
      expect(analysis.overdueByDays).toHaveProperty('8-14')
      expect(analysis.overdueByDays).toHaveProperty('15-30')
      expect(analysis.overdueByDays).toHaveProperty('30+')
      expect(Array.isArray(analysis.overdueBooks)).toBe(true)
      
      if (analysis.overdueBooks.length > 0) {
        expect(analysis.overdueBooks[0]).toHaveProperty('id')
        expect(analysis.overdueBooks[0]).toHaveProperty('user')
        expect(analysis.overdueBooks[0]).toHaveProperty('copy')
      }
    })

    it('should handle case with no overdue books', async () => {
      const analysis = await getOverdueAnalysis()

      expect(analysis.totalOverdue).toBe(0)
      expect(analysis.overdueByDays['1-7']).toBe(0)
      expect(analysis.overdueByDays['8-14']).toBe(0)
      expect(analysis.overdueByDays['15-30']).toBe(0)
      expect(analysis.overdueByDays['30+']).toBe(0)
      expect(analysis.overdueBooks).toEqual([])
    })

    it('should handle database errors gracefully', async () => {
      const originalFindMany = prisma.borrow.findMany
      prisma.borrow.findMany = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await getOverdueAnalysis()

      expect(result).toEqual({
        totalOverdue: 0,
        overdueByDays: {
          '1-7': 0,
          '8-14': 0,
          '15-30': 0,
          '30+': 0
        },
        overdueBooks: []
      })

      prisma.borrow.findMany = originalFindMany
    })
  })
}) 