import {
  createNotification,
  getUserNotifications,
  getUnreadNotificationsCount,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
  createBulkNotifications,
  notifyUsersByRole
} from '@/lib/actions/notifications'
import {
  createTestUser,
  createTestAdmin,
  createTestLibrarian,
  createTestNotification,
  mockConsole
} from '../utils/test-helpers'
import { prisma } from '../setup/test-setup'

mockConsole()

describe('Notification Actions', () => {
  describe('createNotification', () => {
    it('should create notification successfully', async () => {
      const user = await createTestUser()
      const notificationData = {
        userId: user.id,
        type: 'SYSTEM' as const,
        title: 'Test Notification',
        message: 'This is a test notification'
      }

      const result = await createNotification(notificationData)

      expect(result.success).toBe(true)
      expect(result.data).toMatchObject({
        userId: user.id,
        type: 'SYSTEM',
        title: 'Test Notification',
        message: 'This is a test notification',
        read: false
      })
      expect(result.message).toBe('Notification created successfully')

      // Verify notification exists in database
      const notification = await prisma.notification.findUnique({
        where: { id: result.data.id }
      })
      expect(notification).toBeTruthy()
    })

    it('should handle database errors gracefully', async () => {
      const user = await createTestUser()
      const originalCreate = prisma.notification.create
      prisma.notification.create = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await createNotification({
        userId: user.id,
        type: 'SYSTEM',
        title: 'Test',
        message: 'Test'
      })

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to create notification')

      prisma.notification.create = originalCreate
    })
  })

  describe('getUserNotifications', () => {
    it('should return user notifications ordered by creation date (newest first)', async () => {
      const user = await createTestUser()
      
      const notification1 = await createTestNotification(user.id, { title: 'First' })
      await new Promise(resolve => setTimeout(resolve, 10))
      const notification2 = await createTestNotification(user.id, { title: 'Second' })
      await new Promise(resolve => setTimeout(resolve, 10))
      const notification3 = await createTestNotification(user.id, { title: 'Third' })

      // Create notification for different user
      const otherUser = await createTestUser({ email: '<EMAIL>' })
      await createTestNotification(otherUser.id, { title: 'Other User' })

      const result = await getUserNotifications(user.id)

      expect(result).toHaveLength(3)
      expect(result[0].title).toBe('Third') // Newest first
      expect(result[1].title).toBe('Second')
      expect(result[2].title).toBe('First')
    })

    it('should return empty array when user has no notifications', async () => {
      const user = await createTestUser()

      const result = await getUserNotifications(user.id)

      expect(result).toEqual([])
    })

    it('should handle database errors gracefully', async () => {
      const user = await createTestUser()
      const originalFindMany = prisma.notification.findMany
      prisma.notification.findMany = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await getUserNotifications(user.id)

      expect(result).toEqual([])
      expect(console.error).toHaveBeenCalled()

      prisma.notification.findMany = originalFindMany
    })
  })

  describe('getUnreadNotificationsCount', () => {
    it('should return correct unread notifications count', async () => {
      const user = await createTestUser()

      await createTestNotification(user.id, { read: false })
      await createTestNotification(user.id, { read: false })
      await createTestNotification(user.id, { read: true }) // Should not be counted

      // Create notification for different user
      const otherUser = await createTestUser({ email: '<EMAIL>' })
      await createTestNotification(otherUser.id, { read: false })

      const result = await getUnreadNotificationsCount(user.id)

      expect(result).toBe(2)
    })

    it('should return 0 when user has no unread notifications', async () => {
      const user = await createTestUser()

      await createTestNotification(user.id, { read: true })

      const result = await getUnreadNotificationsCount(user.id)

      expect(result).toBe(0)
    })

    it('should return 0 when user has no notifications', async () => {
      const user = await createTestUser()

      const result = await getUnreadNotificationsCount(user.id)

      expect(result).toBe(0)
    })

    it('should handle database errors gracefully', async () => {
      const user = await createTestUser()
      const originalCount = prisma.notification.count
      prisma.notification.count = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await getUnreadNotificationsCount(user.id)

      expect(result).toBe(0)
      expect(console.error).toHaveBeenCalled()

      prisma.notification.count = originalCount
    })
  })

  describe('markNotificationAsRead', () => {
    it('should mark notification as read successfully', async () => {
      const user = await createTestUser()
      const notification = await createTestNotification(user.id, { read: false })

      const result = await markNotificationAsRead(notification.id)

      expect(result.success).toBe(true)
      expect(result.message).toBe('Notification marked as read')

      // Verify notification was marked as read
      const updatedNotification = await prisma.notification.findUnique({
        where: { id: notification.id }
      })
      expect(updatedNotification?.read).toBe(true)
    })

    it('should handle database errors gracefully', async () => {
      const user = await createTestUser()
      const notification = await createTestNotification(user.id)

      const originalUpdate = prisma.notification.update
      prisma.notification.update = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await markNotificationAsRead(notification.id)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to mark notification as read')

      prisma.notification.update = originalUpdate
    })
  })

  describe('markAllNotificationsAsRead', () => {
    it('should mark all user notifications as read', async () => {
      const user = await createTestUser()

      await createTestNotification(user.id, { read: false })
      await createTestNotification(user.id, { read: false })
      await createTestNotification(user.id, { read: true }) // Already read

      // Create notification for different user
      const otherUser = await createTestUser({ email: '<EMAIL>' })
      await createTestNotification(otherUser.id, { read: false })

      const result = await markAllNotificationsAsRead(user.id)

      expect(result.success).toBe(true)
      expect(result.message).toBe('All notifications marked as read')

      // Verify all user notifications are read
      const userNotifications = await prisma.notification.findMany({
        where: { userId: user.id }
      })
      expect(userNotifications.every(n => n.read)).toBe(true)

      // Verify other user's notification is unchanged
      const otherNotifications = await prisma.notification.findMany({
        where: { userId: otherUser.id }
      })
      expect(otherNotifications[0].read).toBe(false)
    })

    it('should handle case when user has no unread notifications', async () => {
      const user = await createTestUser()

      await createTestNotification(user.id, { read: true })

      const result = await markAllNotificationsAsRead(user.id)

      expect(result.success).toBe(true)
      expect(result.message).toBe('All notifications marked as read')
    })

    it('should handle database errors gracefully', async () => {
      const user = await createTestUser()
      const originalUpdateMany = prisma.notification.updateMany
      prisma.notification.updateMany = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await markAllNotificationsAsRead(user.id)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to mark all notifications as read')

      prisma.notification.updateMany = originalUpdateMany
    })
  })

  describe('deleteNotification', () => {
    it('should delete notification successfully', async () => {
      const user = await createTestUser()
      const notification = await createTestNotification(user.id)

      const result = await deleteNotification(notification.id)

      expect(result.success).toBe(true)
      expect(result.message).toBe('Notification deleted successfully')

      // Verify notification was deleted
      const deletedNotification = await prisma.notification.findUnique({
        where: { id: notification.id }
      })
      expect(deletedNotification).toBeNull()
    })

    it('should handle database errors gracefully', async () => {
      const user = await createTestUser()
      const notification = await createTestNotification(user.id)

      const originalDelete = prisma.notification.delete
      prisma.notification.delete = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await deleteNotification(notification.id)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to delete notification')

      prisma.notification.delete = originalDelete
    })
  })

  describe('createBulkNotifications', () => {
    it('should create bulk notifications successfully', async () => {
      const user1 = await createTestUser({ email: '<EMAIL>' })
      const user2 = await createTestUser({ email: '<EMAIL>' })
      const user3 = await createTestUser({ email: '<EMAIL>' })

      const userIds = [user1.id, user2.id, user3.id]
      const notificationData = {
        type: 'SYSTEM' as const,
        title: 'System Announcement',
        message: 'This is a system-wide announcement'
      }

      const result = await createBulkNotifications(userIds, notificationData)

      expect(result.success).toBe(true)
      expect(result.message).toBe('Created 3 notifications')

      // Verify notifications were created for all users
      for (const userId of userIds) {
        const notification = await prisma.notification.findFirst({
          where: {
            userId,
            title: 'System Announcement'
          }
        })
        expect(notification).toBeTruthy()
        expect(notification?.message).toBe('This is a system-wide announcement')
      }
    })

    it('should handle empty user list', async () => {
      const result = await createBulkNotifications([], {
        type: 'SYSTEM',
        title: 'Test',
        message: 'Test'
      })

      expect(result.success).toBe(true)
      expect(result.message).toBe('Created 0 notifications')
    })

    it('should handle database errors gracefully', async () => {
      const user = await createTestUser()
      const originalCreateMany = prisma.notification.createMany
      prisma.notification.createMany = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await createBulkNotifications([user.id], {
        type: 'SYSTEM',
        title: 'Test',
        message: 'Test'
      })

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to create bulk notifications')

      prisma.notification.createMany = originalCreateMany
    })
  })

  describe('notifyUsersByRole', () => {
    it('should notify users by role successfully', async () => {
      const admin1 = await createTestAdmin()
      const admin2 = await createTestUser({ 
        email: '<EMAIL>', 
        role: 'ADMIN' 
      })
      const librarian = await createTestLibrarian()
      const user = await createTestUser({ email: '<EMAIL>' })

      const notificationData = {
        type: 'SYSTEM' as const,
        title: 'Admin Notification',
        message: 'This is for admins only'
      }

      const result = await notifyUsersByRole(['ADMIN'], notificationData)

      expect(result.success).toBe(true)
      expect(result.message).toBe('Created 2 notifications')

      // Verify notifications were created for admins
      const adminNotifications = await prisma.notification.findMany({
        where: {
          userId: { in: [admin1.id, admin2.id] },
          title: 'Admin Notification'
        }
      })
      expect(adminNotifications).toHaveLength(2)

      // Verify no notification for librarian or user
      const otherNotifications = await prisma.notification.findMany({
        where: {
          userId: { in: [librarian.id, user.id] },
          title: 'Admin Notification'
        }
      })
      expect(otherNotifications).toHaveLength(0)
    })

    it('should notify multiple roles', async () => {
      const admin = await createTestAdmin()
      const librarian = await createTestLibrarian()
      const user = await createTestUser({ email: '<EMAIL>' })

      const result = await notifyUsersByRole(['ADMIN', 'LIBRARIAN'], {
        type: 'SYSTEM',
        title: 'Staff Notification',
        message: 'This is for staff only'
      })

      expect(result.success).toBe(true)
      expect(result.message).toBe('Created 2 notifications')

      // Verify notifications for admin and librarian
      const staffNotifications = await prisma.notification.findMany({
        where: {
          userId: { in: [admin.id, librarian.id] },
          title: 'Staff Notification'
        }
      })
      expect(staffNotifications).toHaveLength(2)

      // Verify no notification for regular user
      const userNotifications = await prisma.notification.findMany({
        where: {
          userId: user.id,
          title: 'Staff Notification'
        }
      })
      expect(userNotifications).toHaveLength(0)
    })

    it('should handle case when no users found with specified roles', async () => {
      await createTestUser({ email: '<EMAIL>' })

      const result = await notifyUsersByRole(['ADMIN'], {
        type: 'SYSTEM',
        title: 'Admin Notification',
        message: 'Test'
      })

      expect(result.success).toBe(true)
      expect(result.message).toBe('No users found with specified roles')
    })

    it('should handle database errors gracefully', async () => {
      const admin = await createTestAdmin()
      const originalFindMany = prisma.user.findMany
      prisma.user.findMany = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await notifyUsersByRole(['ADMIN'], {
        type: 'SYSTEM',
        title: 'Test',
        message: 'Test'
      })

      expect(result.success).toBe(false)
      expect(result.error).toBe('Error notifying users by role')

      prisma.user.findMany = originalFindMany
    })
  })
}) 