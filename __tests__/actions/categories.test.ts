import {
  getCategories,
  getCategoryById,
  getCategoryBySlug,
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoriesWithCounts
} from '@/lib/actions/categories'
import {
  createTestCategory,
  createTestBook,
  mockConsole
} from '../utils/test-helpers'
import { prisma } from '../setup/test-setup'

// Mock Next.js functions
jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
  revalidateTag: jest.fn(),
}))

// Setup console mocking
mockConsole()

describe('Category Actions', () => {
  describe('getCategories', () => {
    it('should return all categories ordered by name', async () => {
      await createTestCategory({ name: 'Science' })
      await createTestCategory({ name: 'Fiction' })
      await createTestCategory({ name: 'Biography' })

      const result = await getCategories()

      expect(result).toHaveLength(3)
      expect(result[0].name).toBe('Biography')
      expect(result[1].name).toBe('Fiction')
      expect(result[2].name).toBe('Science')
    })

    it('should return empty array when no categories exist', async () => {
      const result = await getCategories()

      expect(result).toEqual([])
    })

    it('should handle database errors gracefully', async () => {
      const originalFindMany = prisma.category.findMany
      prisma.category.findMany = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await getCategories()

      expect(result).toEqual([])

      prisma.category.findMany = originalFindMany
    })
  })

  describe('getCategoryById', () => {
    it('should return category by ID', async () => {
      const category = await createTestCategory({ name: 'Test Category' })

      const result = await getCategoryById(category.id)

      expect(result).toBeTruthy()
      expect(result?.id).toBe(category.id)
      expect(result?.name).toBe('Test Category')
    })

    it('should return null for non-existent category', async () => {
      const result = await getCategoryById('non-existent-id')

      expect(result).toBeNull()
    })

    it('should handle database errors gracefully', async () => {
      const originalFindUnique = prisma.category.findUnique
      prisma.category.findUnique = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await getCategoryById('any-id')

      expect(result).toBeNull()

      prisma.category.findUnique = originalFindUnique
    })
  })

  describe('getCategoryBySlug', () => {
    it('should return category by slug', async () => {
      const category = await createTestCategory({ name: 'Test Category' })

      const result = await getCategoryBySlug(category.slug)

      expect(result).toBeTruthy()
      expect(result?.slug).toBe(category.slug)
    })

    it('should return null for non-existent slug', async () => {
      const result = await getCategoryBySlug('non-existent-slug')

      expect(result).toBeNull()
    })

    it('should handle database errors gracefully', async () => {
      const originalFindUnique = prisma.category.findUnique
      prisma.category.findUnique = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await getCategoryBySlug('any-slug')

      expect(result).toBeNull()

      prisma.category.findUnique = originalFindUnique
    })
  })

  describe('createCategory', () => {
    it('should create a new category successfully', async () => {
      const categoryData = {
        name: 'New Category',
        description: 'A new test category'
      }

      const result = await createCategory(categoryData)

      expect(result.success).toBe(true)
      expect(result.data).toMatchObject({
        name: categoryData.name,
        description: categoryData.description
      })
      expect(result.message).toBe('Category created successfully')
      
      // Verify category was created
      const category = await prisma.category.findFirst({
        where: { name: categoryData.name }
      })
      expect(category).toBeTruthy()
    })

    it('should create category without description', async () => {
      const result = await createCategory({ name: 'No Description Category' })

      expect(result.success).toBe(true)
      expect(result.data?.name).toBe('No Description Category')
      expect(result.data?.description).toBeNull()
    })

    it('should generate correct slug from name', async () => {
      const result = await createCategory({ name: 'Science Fiction & Fantasy' })

      expect(result.success).toBe(true)
      expect(result.data?.slug).toBe('science-fiction-fantasy')
    })

    it('should fail when category with same name already exists', async () => {
      await createTestCategory({ name: 'Existing Category' })

      const result = await createCategory({ name: 'Existing Category' })

      expect(result.success).toBe(false)
      expect(result.error).toBe('A category with this name already exists')
    })

    it('should handle database errors gracefully', async () => {
      // Test the function doesn't crash
      const result = await createCategory({ name: 'Test Category' })

      expect(result).toBeDefined()
      expect(typeof result.success).toBe('boolean')
    })
  })

  describe('updateCategory', () => {
    it('should update category successfully', async () => {
      const category = await createTestCategory({ name: 'Original Name' })
      const updateData = {
        name: 'Updated Name',
        description: 'Updated description'
      }

      const result = await updateCategory(category.id, updateData)

      expect(result.success).toBe(true)
      expect(result.data).toMatchObject({
        name: updateData.name,
        description: updateData.description
      })
      expect(result.message).toBe('Category updated successfully')
    })

    it('should handle partial updates', async () => {
      const category = await createTestCategory({ name: 'Test Category' })
      const updateData = { description: 'Only description updated' }

      const result = await updateCategory(category.id, updateData)

      expect(result.success).toBe(true)
      expect(result.data?.description).toBe(updateData.description)
    })

    it('should update slug when name is changed', async () => {
      const category = await createTestCategory({ name: 'Old Name' })
      const updateData = { name: 'New Name' }

      const result = await updateCategory(category.id, updateData)

      expect(result.success).toBe(true)
      expect(result.data?.slug).toBe('new-name')
    })

    it('should fail when updating to existing category name', async () => {
      const category1 = await createTestCategory({ name: 'Category 1' })
      const category2 = await createTestCategory({ name: 'Category 2' })

      const result = await updateCategory(category2.id, { name: 'Category 1' })

      expect(result.success).toBe(false)
      expect(result.error).toBe('A category with this name already exists')
    })

    it('should handle database errors gracefully', async () => {
      // Try to update non-existent category to simulate error
      const result = await updateCategory('non-existent-id', { name: 'New Name' })

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to update category')
    })
  })

  describe('deleteCategory', () => {
    it('should delete category successfully when no books assigned', async () => {
      const category = await createTestCategory()

      const result = await deleteCategory(category.id)

      expect(result.success).toBe(true)
      expect(result.message).toBe('Category deleted successfully')
    })

    it('should fail to delete category with assigned books', async () => {
      const category = await createTestCategory()
      const book = await createTestBook()
      
      // Assign book to category
      await prisma.bookCategory.create({
        data: {
          bookId: book.id,
          categoryId: category.id
        }
      })

      const result = await deleteCategory(category.id)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Cannot delete category that has books assigned to it')
    })

    it('should handle database errors gracefully', async () => {
      // Try to delete non-existent category to simulate error
      const result = await deleteCategory('non-existent-id')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to delete category')
    })
  })

  describe('getCategoriesWithCounts', () => {
    it('should return categories with book counts', async () => {
      const category1 = await createTestCategory({ name: 'Category 1' })
      const category2 = await createTestCategory({ name: 'Category 2' })
      
      // Create books in each category
      const book1 = await createTestBook()
      const book2 = await createTestBook()
      const book3 = await createTestBook()
      
      await prisma.bookCategory.createMany({
        data: [
          { bookId: book1.id, categoryId: category1.id },
          { bookId: book2.id, categoryId: category1.id },
          { bookId: book3.id, categoryId: category2.id }
        ]
      })

      const result = await getCategoriesWithCounts()

      expect(result).toHaveLength(2)
      
      const cat1 = result.find(cat => cat.name === 'Category 1')
      const cat2 = result.find(cat => cat.name === 'Category 2')
      
      expect(cat1?._count.books).toBe(2)
      expect(cat2?._count.books).toBe(1)
    })

    it('should return categories with zero counts when no books assigned', async () => {
      await createTestCategory({ name: 'Empty Category' })

      const result = await getCategoriesWithCounts()

      expect(result).toHaveLength(1)
      expect(result[0]._count.books).toBe(0)
    })

    it('should handle database errors gracefully', async () => {
      const originalFindMany = prisma.category.findMany
      prisma.category.findMany = jest.fn().mockRejectedValue(new Error('Database error'))

      const result = await getCategoriesWithCounts()

      expect(result).toEqual([])

      prisma.category.findMany = originalFindMany
    })
  })
}) 