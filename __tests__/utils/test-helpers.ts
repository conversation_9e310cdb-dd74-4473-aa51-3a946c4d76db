import { hashPassword } from '@/lib/utils'
import { prisma } from '../setup/test-setup'
import { Role, BookCondition, BookCopyStatus } from '@prisma/client'

// User test helpers
export async function createTestUser(overrides: Partial<{
  name: string
  email: string
  password: string
  role: Role
  phone?: string
  address?: string
}> = {}) {
  const defaultData = {
    name: 'Test User',
    email: generateRandomEmail(),
    password: 'password123',
    role: 'USER' as Role,
    phone: '************',
    address: '123 Test St'
  }

  const userData = { ...defaultData, ...overrides }
  const hashedPassword = await hashPassword(userData.password)

  return await prisma.user.create({
    data: {
      ...userData,
      password: hashedPassword
    }
  })
}

export async function createTestAdmin() {
  return await createTestUser({
    name: 'Test Admin',
    email: generateRandomEmail(),
    role: 'ADMIN'
  })
}

export async function createTestLibrarian() {
  return await createTestUser({
    name: 'Test Librarian',
    email: generateRandomEmail(),
    role: 'LIBRARIAN'
  })
}

// Category test helpers
export async function createTestCategory(overrides: Partial<{
  name: string
  description?: string
  slug: string
}> = {}) {
  const timestamp = Date.now()
  const random = Math.floor(Math.random() * 1000)
  
  const defaultData = {
    name: `Test Category ${timestamp}-${random}`,
    description: 'A test category'
  }

  const categoryData = { ...defaultData, ...overrides }
  
  // Remove slug from overrides since createCategory generates it
  const { slug, ...createData } = categoryData
  
  return await prisma.category.create({
    data: {
      ...createData,
      slug: slug || `test-category-${timestamp}-${random}`
    }
  })
}

// Book test helpers
export async function createTestBook(overrides: Partial<{
  title: string
  author: string
  isbn?: string
  description?: string
  publisher?: string
  publicationYear?: number
  pages?: number
  language?: string
  coverImageUrl?: string
  pdfUrl?: string
  hasDigitalCopy: boolean
}> = {}) {
  const timestamp = Date.now()
  const random = Math.floor(Math.random() * 1000)
  
  const defaultData = {
    title: `Test Book ${timestamp}-${random}`,
    author: 'Test Author',
    isbn: generateRandomISBN(),
    description: 'A test book description',
    publisher: 'Test Publisher',
    publicationYear: 2023,
    pages: 200,
    language: 'English',
    hasDigitalCopy: false
  }

  return await prisma.book.create({
    data: { ...defaultData, ...overrides }
  })
}

export async function createTestBookWithCategory(categoryId?: string) {
  let category
  
  if (categoryId) {
    category = await prisma.category.findUnique({ where: { id: categoryId } })
    if (!category) throw new Error('Category not found')
  } else {
    category = await createTestCategory()
  }

  const book = await createTestBook()
  
  await prisma.bookCategory.create({
    data: {
      bookId: book.id,
      categoryId: category.id
    }
  })

  return { book, category }
}

export async function createTestBookCopy(bookId: string, overrides: Partial<{
  copyCode: string
  condition: BookCondition
  location?: string
  status: BookCopyStatus
}> = {}) {
  const defaultData = {
    copyCode: `COPY-${Math.random().toString(36).substr(2, 9).toUpperCase()}`,
    condition: 'GOOD' as BookCondition,
    location: 'A-1-1',
    status: 'AVAILABLE' as BookCopyStatus
  }

  return await prisma.bookCopy.create({
    data: {
      bookId,
      ...defaultData,
      ...overrides
    }
  })
}

// Borrow request test helpers
export async function createTestBorrowRequest(userId: string, copyId: string, overrides: Partial<{
  requestedReturnDate?: Date
  status: any
}> = {}) {
  const defaultData = {
    requestedReturnDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
    status: 'PENDING' as any
  }

  return await prisma.borrowRequest.create({
    data: {
      userId,
      copyId,
      ...defaultData,
      ...overrides
    }
  })
}

// Borrow test helpers
export async function createTestBorrow(userId: string, copyId: string, overrides: Partial<{
  requestId?: string
  borrowDate: Date
  dueDate: Date
  returnDate?: Date
  status: any
}> = {}) {
  const now = new Date()
  const defaultData = {
    borrowDate: now,
    dueDate: new Date(now.getTime() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
    status: 'ACTIVE' as any
  }

  return await prisma.borrow.create({
    data: {
      userId,
      copyId,
      ...defaultData,
      ...overrides
    }
  })
}

// Notification test helpers
export async function createTestNotification(userId: string, overrides: Partial<{
  type: any
  title: string
  message: string
  read: boolean
}> = {}) {
  const defaultData = {
    type: 'SYSTEM' as any,
    title: 'Test Notification',
    message: 'This is a test notification',
    read: false
  }

  return await prisma.notification.create({
    data: {
      userId,
      ...defaultData,
      ...overrides
    }
  })
}

// Helper to wait for async operations
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Mock console methods for tests - improved version
export function mockConsole() {
  let consoleErrorSpy: jest.SpyInstance
  
  beforeEach(() => {
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    if (consoleErrorSpy) {
      consoleErrorSpy.mockRestore()
    }
  })
  
  return () => consoleErrorSpy
}

// Generate random data
export function generateRandomEmail() {
  return `test${Math.random().toString(36).substr(2, 9)}@example.com`
}

export function generateRandomISBN() {
  return `978-0-${Math.random().toString(36).substr(2, 6)}-${Math.random().toString(36).substr(2, 2)}-${Math.random().toString(36).substr(2, 1)}`
} 