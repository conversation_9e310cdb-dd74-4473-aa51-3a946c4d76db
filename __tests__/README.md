# Testing Documentation

## Overview

This project uses **Jest** with **TypeScript** for comprehensive testing of all server actions and utility functions. The test suite covers all backend functionality including user management, book operations, borrowing workflows, notifications, and dashboard analytics.

## Test Structure

```
__tests__/
├── setup/
│   └── test-setup.ts          # Global test configuration and database setup
├── utils/
│   └── test-helpers.ts        # Test helper functions and utilities
├── actions/
│   ├── users.test.ts          # User management tests
│   ├── books.test.ts          # Book and book copy management tests
│   ├── categories.test.ts     # Category management tests
│   ├── borrows.test.ts        # Borrowing workflow tests
│   ├── notifications.test.ts  # Notification system tests
│   └── dashboard.test.ts      # Dashboard and analytics tests
└── utils/
    └── utils.test.ts          # Utility function tests
```

## Test Database

The tests use a separate SQLite test database (`test.db`) that is:
- Created fresh for each test run
- Cleaned between each test to ensure isolation
- Automatically removed after tests complete

## Running Tests

### Install Dependencies
```bash
npm install
```

### Run All Tests
```bash
npm test
```

### Run Tests in Watch Mode
```bash
npm run test:watch
```

### Run Tests with Coverage Report
```bash
npm run test:coverage
```

### Setup Test Database (if needed)
```bash
npm run db:test
```

## Test Coverage

The test suite covers:

### ✅ User Management
- User registration and authentication
- Profile updates and password changes
- User role management
- User statistics and deletion

### ✅ Book Management
- Book creation, updates, and deletion
- Book search with advanced filtering
- Book copy management
- Featured books functionality

### ✅ Category Management
- Category CRUD operations
- Category slug generation
- Category-book associations
- Category statistics

### ✅ Borrowing System
- Borrow request creation and validation
- Admin approval/rejection workflow
- Active borrow management
- Book return processing
- Overdue book detection and notifications

### ✅ Notification System
- Notification creation and delivery
- Read/unread status management
- Bulk notifications
- Role-based notifications

### ✅ Dashboard & Analytics
- Admin dashboard statistics
- Popular books analysis
- Recent activities tracking
- Borrowing trends (12 months)
- Category distribution
- User engagement metrics
- Overdue analysis with categorization

### ✅ Utility Functions
- Password hashing and verification
- Date formatting and calculations
- String utilities (slugs, copy codes)
- Search term normalization
- Text highlighting

## Test Helpers

The test suite includes comprehensive helper functions:

- **User Helpers**: Create test users with different roles
- **Book Helpers**: Create test books, categories, and copies
- **Borrow Helpers**: Create test borrow requests and records
- **Notification Helpers**: Create test notifications
- **Database Helpers**: Database cleanup and setup utilities
- **Mock Helpers**: Console mocking for error testing

## Key Testing Features

### 🔒 **Database Isolation**
Each test runs with a clean database state, ensuring no interference between tests.

### 🎯 **Comprehensive Coverage**
Tests cover both success and failure scenarios, including:
- Valid input handling
- Invalid input validation
- Database error scenarios
- Edge cases and boundary conditions

### 🚨 **Error Handling**
All server actions are tested for graceful error handling with proper error messages.

### 📊 **Performance Testing**
Tests verify that functions handle large datasets and complex queries efficiently.

### 🔄 **Workflow Testing**
Complex workflows like the borrowing process are tested end-to-end.

## Test Configuration

### Jest Configuration (jest.config.js)
- TypeScript support with `ts-jest`
- Path mapping for `@/` imports
- 30-second timeout for database operations
- Automatic mocking and cleanup
- Coverage collection for source files only

### Test Environment
- Node.js environment for server-side testing
- SQLite in-memory database for speed
- Automatic schema migration for each test run

## Running Individual Test Suites

```bash
# Run specific test file
npm test users.test.ts

# Run tests for specific functionality
npm test -- --testNamePattern="user registration"

# Run tests with debugging output
npm test -- --verbose

# Run tests and watch for changes
npm test -- --watch --testPathPattern=users
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Ensure the test database can be created
   - Check file permissions in the project directory

2. **Import Path Issues**
   - Verify the `@/` path mapping in `jest.config.js`
   - Check that all imports use the correct paths

3. **Timeout Issues**
   - Increase the timeout in `jest.config.js` if needed
   - Check for hanging database connections

4. **Mock Issues**
   - Ensure mocks are properly restored after each test
   - Check that console mocking is working correctly

### Debug Mode

To run tests with additional debugging:

```bash
# Enable debug output
DEBUG=* npm test

# Run single test with detailed output
npm test -- --testNamePattern="specific test name" --verbose
```

## Contributing

When adding new tests:

1. Follow the existing test structure and naming conventions
2. Use the provided test helpers for consistency
3. Include both positive and negative test cases
4. Test error scenarios and edge cases
5. Ensure proper cleanup in test teardown
6. Update this documentation if adding new test categories

## Performance

The test suite is optimized for speed:
- Uses in-memory SQLite for fast database operations
- Parallel test execution where possible
- Efficient test data creation with minimal dependencies
- Automatic cleanup to prevent memory leaks

Average test run time: **< 30 seconds** for the full suite. 