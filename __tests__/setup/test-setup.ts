import { PrismaClient } from '@prisma/client'
import { execSync } from 'child_process'
import { join } from 'path'
import fs from 'fs'

// Mock Next.js server functions for testing
jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
  revalidateTag: jest.fn(),
}))

// Setup test database
const testDbPath = join(process.cwd(), 'prisma', 'test.db')

// Set environment variable for test database
process.env.DATABASE_URL = `file:${testDbPath}`

// Global test database instance
declare global {
  var __prisma: PrismaClient | undefined
}

export const prisma = globalThis.__prisma || new PrismaClient()

if (process.env.NODE_ENV !== 'production') {
  globalThis.__prisma = prisma
}

// Setup and teardown functions
export async function setupTestDb() {
  try {
    // Ensure the prisma directory exists
    const prismaDir = join(process.cwd(), 'prisma')
    if (!fs.existsSync(prismaDir)) {
      fs.mkdirSync(prismaDir, { recursive: true })
    }

    // Remove existing test database
    if (fs.existsSync(testDbPath)) {
      try {
        fs.unlinkSync(testDbPath)
      } catch (error) {
        // If file is locked, wait a bit and try again
        await new Promise(resolve => setTimeout(resolve, 100))
        if (fs.existsSync(testDbPath)) {
          fs.unlinkSync(testDbPath)
        }
      }
    }

    // Push schema to test database
    execSync(`DATABASE_URL="file:${testDbPath}" npx prisma db push --force-reset`, {
      stdio: 'pipe'
    })

    // Connect to test database
    await prisma.$connect()
  } catch (error) {
    console.error('Error setting up test database:', error)
    throw error
  }
}

export async function cleanupTestDb() {
  try {
    await prisma.$disconnect()
  } catch (error) {
    // Ignore disconnect errors
  }
  
  // Remove test database file
  if (fs.existsSync(testDbPath)) {
    try {
      fs.unlinkSync(testDbPath)
    } catch (error) {
      // If file is locked, wait a bit and try again
      await new Promise(resolve => setTimeout(resolve, 100))
      try {
        if (fs.existsSync(testDbPath)) {
          fs.unlinkSync(testDbPath)
        }
      } catch (finalError) {
        // Ignore final cleanup errors
      }
    }
  }
}

export async function clearDatabase() {
  try {
    // Delete all data in order to respect foreign key constraints
    await prisma.downloadLog.deleteMany({})
    await prisma.notification.deleteMany({})
    await prisma.borrow.deleteMany({})
    await prisma.borrowRequest.deleteMany({})
    await prisma.bookCopy.deleteMany({})
    await prisma.bookCategory.deleteMany({})
    await prisma.book.deleteMany({})
    await prisma.category.deleteMany({})
    await prisma.user.deleteMany({})
  } catch (error) {
    console.error('Error clearing database:', error)
    // If clearing fails, recreate the database
    await cleanupTestDb()
    await setupTestDb()
  }
}

// Global setup and teardown
beforeAll(async () => {
  await setupTestDb()
})

afterAll(async () => {
  await cleanupTestDb()
})

afterEach(async () => {
  await clearDatabase()
}) 